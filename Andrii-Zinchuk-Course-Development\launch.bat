@echo off
title And<PERSON><PERSON> Platform Launcher
color 0A

echo.
echo ========================================
echo   And<PERSON><PERSON> Learning Platform
echo   Enhanced with Manus AI Capabilities
echo ========================================
echo.

echo Starting the personalized learning platform...
echo.

REM Check if the main HTML file exists
if not exist "index.html" (
    echo ERROR: index.html not found!
    echo Please make sure you're running this from the correct directory.
    pause
    exit /b 1
)

echo [1] Opening Main Dashboard...
start "" "index.html"

timeout /t 2 /nobreak >nul

echo [2] Opening Analytics Dashboard...
start "" "data-analysis.html"

echo.
echo ========================================
echo Platform launched successfully!
echo.
echo Main Features:
echo - Personalized Course Recommendations
echo - AI-Powered Learning Analytics  
echo - Career Development Roadmap
echo - Skills Assessment and Tracking
echo - Manus AI Integration
echo.
echo Navigate between:
echo - Main Dashboard (index.html)
echo - Analytics Dashboard (data-analysis.html)
echo ========================================
echo.

echo Press any key to exit launcher...
pause >nul
