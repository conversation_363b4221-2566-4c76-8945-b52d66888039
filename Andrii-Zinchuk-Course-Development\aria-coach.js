// AI Coach ARIA - Step 1: Basic Navigation and Initialization
// Enhanced Career Transformation Platform for <PERSON><PERSON><PERSON>

// Global state management with Manus AI Logic
let currentSection = 'dashboard';
let ariaConversationHistory = [];
let userProgress = {
    careerProgress: 35,
    completedCourses: 2,
    totalCourses: 5,
    achievements: [],
    currentGoals: [],
    industryFocus: 'automotive-electronics',
    specialization: 'materials-informatics',
    manusAIInsights: {
        strengthsAnalysis: ['15+ years manufacturing experience', 'PhD in Materials Engineering', 'SMT/CBA expertise', 'PVD coating knowledge', 'Quality control mastery'],
        opportunityGaps: ['Python programming', 'Machine learning fundamentals', 'Computer vision applications', 'Cloud ML platforms', 'Data visualization'],
        marketPositioning: 'Unique combination of deep manufacturing domain knowledge with emerging AI skills - rare in market',
        competitiveAdvantage: 'Can bridge gap between traditional manufacturing and AI-driven Industry 4.0',
        riskFactors: ['Rapid technology evolution', 'Competition from CS graduates', 'Need for continuous learning'],
        successProbability: 87
    }
};

// Enhanced market intelligence data with 2025 trends and Manus AI Analysis
const marketIntelligence = {
    poland: {
        gdansk: {
            junior: 65000, senior: 105000, growth: 15,
            aiJobs: 245, companies: ['Aptiv', 'Intel', 'Asseco', 'Comarch', 'Olivia Business Centre'],
            advantages: ['Close to Andrii', 'Growing tech scene', 'Lower cost of living', 'Baltic Sea access'],
            challenges: ['Smaller market', 'Limited AI companies', 'Language barrier for international roles'],
            manusScore: 92 // High due to proximity and growing opportunities
        },
        warsaw: {
            junior: 75000, senior: 125000, growth: 18,
            aiJobs: 1250, companies: ['Google', 'Microsoft', 'Amazon', 'Allegro', 'CD Projekt', 'Asseco'],
            advantages: ['Largest AI market', 'Highest salaries', 'International companies', 'Startup ecosystem'],
            challenges: ['High competition', 'Expensive living', 'Commute from Gdansk'],
            manusScore: 85 // High opportunities but logistical challenges
        },
        krakow: {
            junior: 70000, senior: 115000, growth: 16,
            aiJobs: 890, companies: ['IBM', 'Motorola', 'State Street', 'UiPath', 'Sabre', 'EPAM'],
            advantages: ['Tech hub', 'Good work-life balance', 'International environment', 'Cultural city'],
            challenges: ['Distance from Gdansk', 'High competition', 'Saturated market'],
            manusScore: 78 // Good but not optimal for Andrii's situation
        }
    },
    automotive: {
        aptiv: {
            aiRoles: 45, avgSalary: 95000, growth: 22,
            focus: ['ADAS', 'Autonomous Driving', 'Smart Manufacturing', 'Computer Vision'],
            opportunity: 'Internal transformation - perfect for Andrii',
            manusAdvantage: 'Already employed, knows company culture, can leverage internal networks',
            transitionPath: 'Manufacturing Engineer → AI Engineer → Senior AI Specialist',
            timeline: '12-18 months'
        },
        continental: {
            aiRoles: 38, avgSalary: 88000, growth: 19,
            focus: ['Sensor Fusion', 'Computer Vision', 'Predictive Analytics', 'Quality Control'],
            opportunity: 'Strong automotive AI research division',
            manusAdvantage: 'Similar automotive background, quality focus aligns with experience'
        },
        bosch: {
            aiRoles: 52, avgSalary: 92000, growth: 21,
            focus: ['Industry 4.0', 'IoT', 'Manufacturing AI', 'Process Optimization'],
            opportunity: 'Leader in Industry 4.0 solutions',
            manusAdvantage: 'Perfect match for manufacturing + AI combination'
        }
    },
    skills: {
        'computer-vision': {
            demand: 94, salary: 110000, growth: 28,
            applications: ['SMT Quality Control', 'Defect Detection', 'AOI Enhancement', 'X-Ray Analysis'],
            manusRelevance: 98, // Perfect match with SMT/CBA experience
            learningPath: ['OpenCV basics', 'Deep Learning for CV', 'Industrial applications', 'Real-time processing'],
            timeToMaster: '6-8 months'
        },
        'materials-informatics': {
            demand: 87, salary: 125000, growth: 35,
            applications: ['New Material Discovery', 'Property Prediction', 'Process Optimization', 'Coating Design'],
            manusRelevance: 96, // PhD in Materials + PVD experience
            learningPath: ['Materials databases', 'ML for materials', 'Quantum chemistry', 'High-throughput computing'],
            timeToMaster: '8-12 months'
        },
        'predictive-maintenance': {
            demand: 91, salary: 98000, growth: 24,
            applications: ['Equipment Failure Prediction', 'PVD Process Optimization', 'Maintenance Scheduling', 'Cost Reduction'],
            manusRelevance: 94, // Direct application to current role
            learningPath: ['Time series analysis', 'IoT integration', 'Sensor data processing', 'Anomaly detection'],
            timeToMaster: '4-6 months'
        },
        'quality-4.0': {
            demand: 89, salary: 102000, growth: 26,
            applications: ['Automated Quality Control', 'Real-time Monitoring', 'Statistical Process Control', 'Six Sigma AI'],
            manusRelevance: 97, // Perfect match with quality engineering background
            learningPath: ['Statistical ML', 'Process control AI', 'Quality metrics automation', 'Lean Six Sigma AI'],
            timeToMaster: '5-7 months'
        },
        'generative-ai': {
            demand: 96, salary: 135000, growth: 42,
            applications: ['Process Documentation', 'Design Optimization', 'Report Generation', 'Training Materials'],
            manusRelevance: 85, // Emerging field with manufacturing applications
            learningPath: ['LLM fundamentals', 'Prompt engineering', 'Fine-tuning', 'Industrial applications'],
            timeToMaster: '3-5 months'
        },
        'llm-applications': {
            demand: 93, salary: 128000, growth: 38,
            applications: ['Technical Documentation', 'Process Analysis', 'Knowledge Management', 'Decision Support'],
            manusRelevance: 88, // Good potential for manufacturing applications
            learningPath: ['NLP basics', 'LLM APIs', 'RAG systems', 'Domain-specific applications'],
            timeToMaster: '4-6 months'
        }
    },
    manusInsights: {
        optimalStrategy: 'Focus on Computer Vision + Materials Informatics combination for unique market position',
        riskMitigation: 'Start with internal Aptiv opportunities while building external portfolio',
        timelineRecommendation: '18-month transformation with 6-month milestones',
        investmentPriority: ['AWS ML Specialty', 'Computer Vision projects', 'Materials AI research'],
        networkingStrategy: 'Trójmiasto AI Meetup + industry conferences + LinkedIn thought leadership'
    }
};

// Enhanced Coursera courses data with 2025 trends and Manus AI Analysis
const courseraCoursesData = {
    topCertifications: [
        {
            id: 'ml-andrew-ng',
            title: 'Machine Learning Specialization (Andrew Ng)',
            provider: 'DeepLearning.AI',
            duration: '3 months',
            cost: '$49/month',
            relevance: 98,
            difficulty: 'Beginner to Intermediate',
            skills: ['Python', 'TensorFlow', 'Supervised Learning', 'Neural Networks'],
            manufacturingRelevance: 'Perfect foundation for manufacturing AI applications',
            roiMonths: 6,
            salaryIncrease: '€15k-25k',
            completionRate: 87,
            rating: 4.9,
            enrollments: '5.2M+',
            trending: true,
            manusAnalysis: {
                strengthsForAndrii: ['World-renowned instructor', 'Solid mathematical foundation', 'Practical programming focus', 'Industry-recognized certificate'],
                manufacturingApplications: ['Quality control algorithms', 'Process optimization', 'Predictive maintenance models', 'Defect classification'],
                careerImpact: 'Essential foundation - opens doors to all AI roles',
                timeCommitment: '10-15 hours/week for 3 months',
                prerequisites: 'Basic programming knowledge helpful but not required',
                nextSteps: ['Deep Learning Specialization', 'Computer Vision courses', 'Manufacturing-specific projects'],
                successFactors: ['Consistent practice', 'Complete all assignments', 'Build portfolio projects', 'Join study groups'],
                riskMitigation: 'Start with audit mode to test commitment before paying'
            }
        },
        {
            id: 'google-data-analytics',
            title: 'Google Data Analytics Professional Certificate',
            provider: 'Google',
            duration: '6 months',
            cost: '$49/month',
            relevance: 94,
            difficulty: 'Beginner',
            skills: ['R', 'SQL', 'Python', 'Tableau', 'Data Visualization'],
            manufacturingRelevance: 'Essential for manufacturing data analysis and reporting',
            roiMonths: 4,
            salaryIncrease: '€12k-20k',
            completionRate: 82,
            rating: 4.7,
            enrollments: '2.8M+',
            trending: true,
            manusAnalysis: {
                strengthsForAndrii: ['Google brand recognition', 'Comprehensive data pipeline coverage', 'Industry-standard tools', 'Job placement assistance'],
                manufacturingApplications: ['KPI dashboards', 'Production analytics', 'Quality metrics visualization', 'Process performance reporting'],
                careerImpact: 'Immediate applicability to current role - can start using skills at Aptiv right away',
                timeCommitment: '10 hours/week for 6 months',
                prerequisites: 'No prior experience required',
                nextSteps: ['Advanced SQL courses', 'Power BI specialization', 'Manufacturing analytics projects'],
                successFactors: ['Practice with real manufacturing data', 'Build portfolio of dashboards', 'Get Google certification'],
                riskMitigation: 'High completion rate and job placement success make this low-risk investment'
            }
        },
        {
            id: 'ibm-data-science',
            title: 'IBM Data Science Professional Certificate',
            provider: 'IBM',
            duration: '5 months',
            cost: '$49/month',
            relevance: 91,
            difficulty: 'Beginner to Intermediate',
            skills: ['Python', 'SQL', 'Machine Learning', 'Data Visualization', 'Statistics'],
            manufacturingRelevance: 'Strong focus on real-world applications in industry',
            roiMonths: 5,
            salaryIncrease: '€14k-22k',
            completionRate: 79,
            rating: 4.6,
            enrollments: '1.9M+',
            trending: false
        },
        {
            id: 'deep-learning-ai',
            title: 'Deep Learning Specialization',
            provider: 'DeepLearning.AI',
            duration: '4 months',
            cost: '$49/month',
            relevance: 92,
            difficulty: 'Intermediate to Advanced',
            skills: ['Deep Learning', 'Computer Vision', 'NLP', 'TensorFlow', 'PyTorch'],
            manufacturingRelevance: 'Advanced AI for quality control and predictive maintenance',
            roiMonths: 8,
            salaryIncrease: '€18k-30k',
            completionRate: 74,
            rating: 4.8,
            enrollments: '1.2M+',
            trending: true
        },
        {
            id: 'generative-ai-google',
            title: 'Generative AI for Everyone',
            provider: 'Google Cloud',
            duration: '2 months',
            cost: '$49/month',
            relevance: 89,
            difficulty: 'Beginner',
            skills: ['Generative AI', 'LLMs', 'Prompt Engineering', 'AI Ethics'],
            manufacturingRelevance: 'Emerging applications in design and process optimization',
            roiMonths: 3,
            salaryIncrease: '€10k-18k',
            completionRate: 91,
            rating: 4.5,
            enrollments: '850k+',
            trending: true,
            new: true
        },
        {
            id: 'aws-ml-specialty',
            title: 'AWS Machine Learning Specialty Prep',
            provider: 'AWS',
            duration: '3 months',
            cost: '$49/month + $300 exam',
            relevance: 96,
            difficulty: 'Advanced',
            skills: ['AWS ML Services', 'SageMaker', 'Model Deployment', 'MLOps'],
            manufacturingRelevance: 'Cloud-based ML solutions for industrial applications',
            roiMonths: 6,
            salaryIncrease: '€20k-35k',
            completionRate: 68,
            rating: 4.7,
            enrollments: '420k+',
            trending: true
        },
        {
            id: 'computer-vision-manufacturing',
            title: 'Computer Vision for Manufacturing Quality Control',
            provider: 'Stanford University',
            duration: '4 months',
            cost: '$79/month',
            relevance: 99,
            difficulty: 'Intermediate to Advanced',
            skills: ['OpenCV', 'Deep Learning', 'Image Processing', 'Defect Detection', 'Real-time Systems'],
            manufacturingRelevance: 'Direct application to SMT/CBA quality control and AOI systems',
            roiMonths: 5,
            salaryIncrease: '€25k-40k',
            completionRate: 76,
            rating: 4.8,
            enrollments: '125k+',
            trending: true,
            new: true,
            manusAnalysis: {
                strengthsForAndrii: ['Perfect match with SMT experience', 'Immediate ROI potential', 'Stanford prestige', 'Industry partnerships'],
                manufacturingApplications: ['SMT solder joint inspection', 'PCB defect classification', 'AOI enhancement', 'X-ray analysis automation'],
                careerImpact: 'Game-changer - positions as expert in high-demand niche',
                timeCommitment: '12-15 hours/week for 4 months',
                prerequisites: 'Basic Python and ML knowledge (Andrew Ng course recommended)',
                nextSteps: ['Real-time systems optimization', 'Edge AI deployment', 'Industry consulting'],
                successFactors: ['Apply to current Aptiv projects', 'Build portfolio with real manufacturing data', 'Present results to management'],
                riskMitigation: 'High relevance and immediate applicability minimize risk'
            }
        },
        {
            id: 'materials-informatics-ai',
            title: 'Materials Informatics and AI for Materials Discovery',
            provider: 'MIT',
            duration: '6 months',
            cost: '$99/month',
            relevance: 97,
            difficulty: 'Advanced',
            skills: ['Materials Science', 'Machine Learning', 'Quantum Chemistry', 'Property Prediction', 'High-throughput Computing'],
            manufacturingRelevance: 'Revolutionary for PVD coating optimization and new materials development',
            roiMonths: 8,
            salaryIncrease: '€30k-50k',
            completionRate: 68,
            rating: 4.9,
            enrollments: '45k+',
            trending: true,
            new: true,
            manusAnalysis: {
                strengthsForAndrii: ['PhD Materials background perfect fit', 'PVD coating expertise advantage', 'MIT prestige', 'Cutting-edge field'],
                manufacturingApplications: ['New coating materials discovery', 'PVD process optimization', 'Property prediction models', 'Materials database mining'],
                careerImpact: 'Positions as thought leader in emerging field with massive potential',
                timeCommitment: '15-20 hours/week for 6 months',
                prerequisites: 'Strong materials science background (PhD level) and basic ML knowledge',
                nextSteps: ['Research publications', 'Industry consulting', 'Startup opportunities'],
                successFactors: ['Leverage existing materials knowledge', 'Collaborate with research institutions', 'Publish findings'],
                riskMitigation: 'Emerging field with high growth potential but requires significant time investment'
            }
        },
        {
            id: 'edge-ai-manufacturing',
            title: 'Edge AI for Industrial IoT and Manufacturing',
            provider: 'NVIDIA',
            duration: '3 months',
            cost: '$69/month',
            relevance: 96,
            difficulty: 'Intermediate',
            skills: ['Edge Computing', 'TensorRT', 'Industrial IoT', 'Real-time AI', 'NVIDIA Jetson'],
            manufacturingRelevance: 'Perfect for real-time quality control and predictive maintenance systems',
            roiMonths: 4,
            salaryIncrease: '€20k-35k',
            completionRate: 81,
            rating: 4.7,
            enrollments: '78k+',
            trending: true,
            new: true,
            manusAnalysis: {
                strengthsForAndrii: ['NVIDIA industry leadership', 'Real-time applications focus', 'Hardware-software integration', 'Growing market demand'],
                manufacturingApplications: ['Real-time defect detection', 'Edge-based predictive maintenance', 'Smart factory implementations', 'Autonomous quality systems'],
                careerImpact: 'Positions at forefront of Industry 4.0 transformation',
                timeCommitment: '10-12 hours/week for 3 months',
                prerequisites: 'Basic AI/ML knowledge and some hardware understanding',
                nextSteps: ['NVIDIA certification', 'Edge AI consulting', 'Smart factory projects'],
                successFactors: ['Hands-on hardware projects', 'Industry partnerships', 'Real deployment experience'],
                riskMitigation: 'NVIDIA backing and growing edge AI market provide strong foundation'
            }
        },
        {
            id: 'generative-ai-manufacturing',
            title: 'Generative AI Applications in Manufacturing',
            provider: 'Google Cloud',
            duration: '2 months',
            cost: '$59/month',
            relevance: 88,
            difficulty: 'Beginner to Intermediate',
            skills: ['Large Language Models', 'Prompt Engineering', 'Process Documentation', 'Design Optimization', 'AI Ethics'],
            manufacturingRelevance: 'Revolutionary for process documentation, training, and design optimization',
            roiMonths: 3,
            salaryIncrease: '€15k-28k',
            completionRate: 89,
            rating: 4.6,
            enrollments: '156k+',
            trending: true,
            new: true,
            manusAnalysis: {
                strengthsForAndrii: ['Fastest growing AI field', 'Immediate practical applications', 'Google Cloud integration', 'Low barrier to entry'],
                manufacturingApplications: ['Automated process documentation', 'Training material generation', 'Quality report automation', 'Design optimization'],
                careerImpact: 'Positions as early adopter in revolutionary technology',
                timeCommitment: '8-10 hours/week for 2 months',
                prerequisites: 'Basic understanding of AI concepts',
                nextSteps: ['Custom model fine-tuning', 'Enterprise AI implementation', 'AI strategy consulting'],
                successFactors: ['Focus on manufacturing use cases', 'Build practical applications', 'Demonstrate ROI'],
                riskMitigation: 'Rapid adoption and clear business value minimize risk'
            }
        }
    ],
    emergingTrends2025: [
        {
            trend: 'Generative AI in Manufacturing',
            growth: 156,
            applications: ['Design Optimization', 'Process Documentation', 'Quality Reports'],
            relevanceToAndrii: 95
        },
        {
            trend: 'Edge AI for Industrial IoT',
            growth: 134,
            applications: ['Real-time Quality Control', 'Predictive Maintenance', 'Safety Monitoring'],
            relevanceToAndrii: 97
        },
        {
            trend: 'AI-Powered Digital Twins',
            growth: 128,
            applications: ['Process Simulation', 'Predictive Analytics', 'Virtual Testing'],
            relevanceToAndrii: 93
        },
        {
            trend: 'Sustainable AI in Manufacturing',
            growth: 142,
            applications: ['Energy Optimization', 'Waste Reduction', 'Carbon Footprint Analysis'],
            relevanceToAndrii: 88
        }
    ],
    platformComparison: {
        coursera: {
            strengths: ['University partnerships', 'Professional certificates', 'Financial aid'],
            weaknesses: ['Higher cost', 'Less hands-on projects'],
            bestFor: 'Structured learning with credentials',
            manufacturingCourses: 245,
            rating: 4.6
        },
        edx: {
            strengths: ['MIT/Harvard courses', 'Free audit options', 'MicroMasters'],
            weaknesses: ['Less industry focus', 'Fewer practical projects'],
            bestFor: 'Academic depth and theory',
            manufacturingCourses: 189,
            rating: 4.5
        },
        udacity: {
            strengths: ['Industry projects', 'Nanodegrees', 'Mentor support'],
            weaknesses: ['Higher cost', 'Less academic rigor'],
            bestFor: 'Practical skills and job readiness',
            manufacturingCourses: 67,
            rating: 4.3
        },
        linkedin: {
            strengths: ['Professional network', 'Skill badges', 'Career integration'],
            weaknesses: ['Surface-level content', 'Limited depth'],
            bestFor: 'Quick skill updates and networking',
            manufacturingCourses: 156,
            rating: 4.2
        }
    }
};

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 AI Coach ARIA initializing...');

    initializeParticles();
    setupNavigation();
    initializeARIA();
    loadUserProgress();
    initializeDashboard();
    initializeCourses();
    initializeCertifications();
    initializePortfolio();
    initializeNetworking();
    initializeRoadmap();
    initializeIndustry40();
    initializeMarketIntel();
    initializeManusInsights();

    // Initialize ARIA AI Agent
    ariaAgent = new ARIAAgent();

    console.log('✅ ARIA platform ready!');
});

// Initialize particle background
function initializeParticles() {
    if (typeof particlesJS !== 'undefined') {
        particlesJS('particles-js', {
            particles: {
                number: { value: 60, density: { enable: true, value_area: 800 } },
                color: { value: "#ffffff" },
                shape: { type: "circle" },
                opacity: { value: 0.4, random: false },
                size: { value: 3, random: true },
                line_linked: {
                    enable: true,
                    distance: 150,
                    color: "#ffffff",
                    opacity: 0.3,
                    width: 1
                },
                move: {
                    enable: true,
                    speed: 4,
                    direction: "none",
                    random: false,
                    straight: false,
                    out_mode: "out",
                    bounce: false
                }
            },
            interactivity: {
                detect_on: "canvas",
                events: {
                    onhover: { enable: true, mode: "repulse" },
                    onclick: { enable: true, mode: "push" },
                    resize: true
                },
                modes: {
                    repulse: { distance: 100, duration: 0.4 },
                    push: { particles_nb: 2 }
                }
            },
            retina_detect: true
        });
    }
}

// Setup navigation functionality
function setupNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    const sections = document.querySelectorAll('.section');

    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const targetSection = this.getAttribute('href').substring(1);

            // Update active nav link
            navLinks.forEach(l => l.classList.remove('active'));
            this.classList.add('active');

            // Show target section
            sections.forEach(section => {
                section.classList.remove('active');
                if (section.id === targetSection) {
                    section.classList.add('active');
                }
            });

            currentSection = targetSection;

            // Notify ARIA about section change
            notifyARIASectionChange(targetSection);

            console.log(`📍 Navigated to: ${targetSection}`);
        });
    });
}

// Initialize ARIA chat system
function initializeARIA() {
    console.log('🤖 Initializing AI Coach ARIA...');

    // Setup chat input
    const chatInput = document.getElementById('chatInput');
    const sendButton = document.getElementById('sendButton');
    const voiceButton = document.getElementById('voiceButton');

    if (chatInput && sendButton) {
        // Send message on Enter key
        chatInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // Send message on button click
        sendButton.addEventListener('click', sendMessage);
    }

    if (voiceButton) {
        voiceButton.addEventListener('click', toggleVoiceInput);
    }

    // Setup voice toggle in navbar
    const voiceToggle = document.getElementById('voiceToggle');
    if (voiceToggle) {
        voiceToggle.addEventListener('click', openVoiceModal);
    }

    // Initialize with welcome message
    setTimeout(() => {
        addARIAMessage("Cześć Andrii! 👋 Jestem ARIA, Twój osobisty AI Coach. Jestem tutaj, aby pomóc Ci w transformacji kariery z Manufacturing Engineering do AI/Data Science. Jak mogę Ci dziś pomóc?");
        showQuickReplies([
            "Pokaż mój postęp",
            "Zaplanuj naukę",
            "Networking events",
            "Aktualizuj portfolio"
        ]);
    }, 1000);
}

// Load user progress from localStorage
function loadUserProgress() {
    const savedProgress = localStorage.getItem('andriiProgress');
    if (savedProgress) {
        userProgress = { ...userProgress, ...JSON.parse(savedProgress) };
    }

    // Initialize with some sample achievements if none exist
    if (userProgress.achievements.length === 0) {
        userProgress.achievements = [
            {
                id: 1,
                title: "Python Fundamentals",
                description: "Ukończono podstawowy kurs Python",
                date: new Date().toISOString(),
                impact: "+10% Data Analysis Skills",
                icon: "fab fa-python"
            },
            {
                id: 2,
                title: "RPA Professional",
                description: "Zdobyto certyfikat SS&C Blue Prism",
                date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
                impact: "+15% Automation Skills",
                icon: "fas fa-robot"
            }
        ];
        saveUserProgress();
    }

    console.log('📊 User progress loaded:', userProgress);
}

// Save user progress to localStorage
function saveUserProgress() {
    localStorage.setItem('andriiProgress', JSON.stringify(userProgress));
}

// Send message to ARIA
function sendMessage() {
    const chatInput = document.getElementById('chatInput');
    const message = chatInput.value.trim();

    if (message) {
        addUserMessage(message);
        chatInput.value = '';

        // Process message with ARIA
        setTimeout(() => {
            processARIAResponse(message);
        }, 1000);
    }
}

// Add user message to chat
function addUserMessage(message) {
    const chatMessages = document.getElementById('chatMessages');
    const messageElement = document.createElement('div');
    messageElement.className = 'chat-message user';
    messageElement.innerHTML = `
        ${message}
        <div class="message-time">${new Date().toLocaleTimeString('pl-PL', { hour: '2-digit', minute: '2-digit' })}</div>
    `;

    chatMessages.appendChild(messageElement);
    chatMessages.scrollTop = chatMessages.scrollHeight;

    // Add to conversation history
    ariaConversationHistory.push({
        type: 'user',
        message: message,
        timestamp: new Date().toISOString()
    });
}

// Add ARIA message to chat
function addARIAMessage(message) {
    const chatMessages = document.getElementById('chatMessages');
    const messageElement = document.createElement('div');
    messageElement.className = 'chat-message aria';
    messageElement.innerHTML = `
        ${message}
        <div class="message-time">${new Date().toLocaleTimeString('pl-PL', { hour: '2-digit', minute: '2-digit' })}</div>
    `;

    chatMessages.appendChild(messageElement);
    chatMessages.scrollTop = chatMessages.scrollHeight;

    // Add to conversation history
    ariaConversationHistory.push({
        type: 'aria',
        message: message,
        timestamp: new Date().toISOString()
    });
}

// Show quick reply buttons
function showQuickReplies(replies) {
    const quickReplies = document.getElementById('quickReplies');
    quickReplies.innerHTML = '';

    replies.forEach(reply => {
        const button = document.createElement('button');
        button.className = 'quick-reply-btn';
        button.textContent = reply;
        button.addEventListener('click', () => {
            addUserMessage(reply);
            setTimeout(() => {
                processARIAResponse(reply);
            }, 1000);
        });
        quickReplies.appendChild(button);
    });
}

// Process ARIA response based on user input
function processARIAResponse(userMessage) {
    const message = userMessage.toLowerCase();
    let response = "";
    let quickReplies = [];

    if (message.includes('postęp') || message.includes('progress')) {
        response = `Świetnie! Twój obecny postęp w transformacji kariery wynosi ${userProgress.careerProgress}%. Ukończyłeś ${userProgress.completedCourses} z ${userProgress.totalCourses} planowanych kursów. Ostatnio zdobyłeś certyfikat RPA Professional - to doskonały krok w kierunku automatyzacji!`;
        quickReplies = ["Pokaż szczegóły", "Następne kroki", "Aktualizuj postęp"];
    } else if (message.includes('nauka') || message.includes('kurs')) {
        response = "Doskonały pomysł! Na podstawie Twojego profilu w inżynierii materiałowej, polecam skupić się na kursach Machine Learning for Manufacturing. Masz już solidne podstawy w Python - czas przejść do poziomu intermediate!";
        quickReplies = ["Pokaż kursy", "Zaplanuj harmonogram", "Sprawdź certyfikacje"];
    } else if (message.includes('networking') || message.includes('wydarzenia')) {
        response = "Networking to kluczowy element transformacji kariery! W Polsce mamy świetne wydarzenia AI. Najbliższe to Global AI Bootcamp Kraków (7 marca) i miesięczne Trójmiasto AI Meetup. Chcesz, żebym pomógł Ci się przygotować?";
        quickReplies = ["Zobacz wydarzenia", "Przygotuj pitch", "LinkedIn strategy"];
    } else if (message.includes('portfolio')) {
        response = "Twoje portfolio to Twoja wizytówka! Z Twoim doświadczeniem w SMT/CBA i PVD, możesz stworzyć unikalne projekty łączące manufacturing z AI. Polecam zacząć od Computer Vision dla Quality Control - to będzie hit!";
        quickReplies = ["Pomysły na projekty", "GitHub setup", "Portfolio review"];
    } else {
        response = "Rozumiem! Jestem tutaj, aby pomóc Ci w każdym aspekcie transformacji kariery. Czy chcesz porozmawiać o konkretnym obszarze rozwoju?";
        quickReplies = ["Kursy i certyfikacje", "Networking", "Portfolio", "Planowanie kariery"];
    }

    addARIAMessage(response);
    showQuickReplies(quickReplies);
}

// Notify ARIA about section changes
function notifyARIASectionChange(section) {
    let contextMessage = "";

    switch(section) {
        case 'dashboard':
            contextMessage = "Widzę, że przeglądasz dashboard. Twój postęp wygląda świetnie! Czy chcesz omówić jakiś konkretny aspekt?";
            break;
        case 'courses':
            contextMessage = "Świetnie, że sprawdzasz kursy Coursera! Te 5 certyfikacji to doskonały plan dla Twojej transformacji. Którym chcesz zacząć?";
            break;
        case 'certifications':
            contextMessage = "Certyfikacje premium to inwestycja w przyszłość! AWS ML Specialty ma najlepszy ROI dla Twojego profilu. Chcesz omówić strategię?";
            break;
        case 'portfolio':
            contextMessage = "Portfolio to Twoja siła! Z doświadczeniem w manufacturing możesz stworzyć projekty, których nikt inny nie ma. Pomogę Ci je zaplanować!";
            break;
        case 'networking':
            contextMessage = "Networking w AI community to klucz do sukcesu! Wydarzenia w Polsce są świetne. Chcesz, żebym pomógł Ci się przygotować?";
            break;
        case 'roadmap':
            contextMessage = "Mapa kariery pokazuje Twoją drogę do sukcesu! 12 miesięcy to realny czas na transformację. Omówimy najbliższe kroki?";
            break;
    }

    if (contextMessage) {
        setTimeout(() => {
            addARIAMessage(contextMessage);
            showQuickReplies(["Tak, pomóż mi", "Pokaż szczegóły", "Inne pytanie"]);
        }, 2000);
    }
}

// Voice input functionality
function toggleVoiceInput() {
    console.log('🎤 Voice input toggled');
    // This will be implemented in next step
    showNotification('Funkcja głosowa będzie dostępna wkrótce!', 'info');
}

function openVoiceModal() {
    const voiceModal = document.getElementById('voiceModal');
    if (voiceModal) {
        voiceModal.style.display = 'block';
    }
}

function closeVoiceModal() {
    const voiceModal = document.getElementById('voiceModal');
    if (voiceModal) {
        voiceModal.style.display = 'none';
    }
}

// Utility function for notifications
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
        <span>${message}</span>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.classList.add('show');
    }, 100);

    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Export functions for global access
window.sendMessage = sendMessage;
window.toggleVoiceInput = toggleVoiceInput;
window.openVoiceModal = openVoiceModal;
window.closeVoiceModal = closeVoiceModal;

// Step 2: Dashboard Charts and Action Buttons
// Initialize dashboard when DOM is ready
function initializeDashboard() {
    console.log('📊 Initializing dashboard charts...');

    createProgressGauge();
    createSkillsRadar();
    createSalaryChart();
    populateAchievements();
    populateTimelinePreview();

    console.log('✅ Dashboard initialized');
}

// Create progress gauge chart
function createProgressGauge() {
    const ctx = document.getElementById('progressGauge');
    if (!ctx) return;

    new Chart(ctx, {
        type: 'doughnut',
        data: {
            datasets: [{
                data: [userProgress.careerProgress, 100 - userProgress.careerProgress],
                backgroundColor: [
                    'linear-gradient(45deg, #4a90e2, #667eea)',
                    '#e9ecef'
                ],
                borderWidth: 0,
                cutout: '75%'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { display: false },
                tooltip: { enabled: false }
            }
        }
    });
}

// Create skills radar chart
function createSkillsRadar() {
    const ctx = document.getElementById('skillsRadar');
    if (!ctx) return;

    new Chart(ctx, {
        type: 'radar',
        data: {
            labels: [
                'Materials Science',
                'Python Programming',
                'Data Analysis',
                'Machine Learning',
                'Quality Engineering',
                'Business Intelligence',
                'Leadership',
                'Manufacturing'
            ],
            datasets: [{
                label: 'Obecny poziom',
                data: [95, 70, 65, 60, 90, 45, 70, 95],
                backgroundColor: 'rgba(74, 144, 226, 0.2)',
                borderColor: '#4a90e2',
                borderWidth: 2,
                pointBackgroundColor: '#4a90e2'
            }, {
                label: 'Poziom docelowy',
                data: [98, 90, 88, 85, 95, 80, 85, 98],
                backgroundColor: 'rgba(255, 107, 107, 0.2)',
                borderColor: '#ff6b6b',
                borderWidth: 2,
                pointBackgroundColor: '#ff6b6b'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { display: false }
            },
            scales: {
                r: {
                    beginAtZero: true,
                    max: 100,
                    ticks: {
                        stepSize: 20,
                        display: false
                    },
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    pointLabels: {
                        font: { size: 11 },
                        color: '#666'
                    }
                }
            }
        }
    });
}

// Create salary projection chart
function createSalaryChart() {
    const ctx = document.getElementById('salaryChart');
    if (!ctx) return;

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['Obecnie', '6 miesięcy', '1 rok', '18 miesięcy', '2 lata'],
            datasets: [{
                label: 'Projekcja wynagrodzeń (EUR)',
                data: [70000, 85000, 105000, 140000, 160000],
                borderColor: '#4a90e2',
                backgroundColor: 'rgba(74, 144, 226, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#4a90e2',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { display: false }
            },
            scales: {
                y: {
                    beginAtZero: false,
                    min: 60000,
                    max: 180000,
                    ticks: {
                        callback: function(value) {
                            return '€' + (value / 1000) + 'k';
                        },
                        color: '#666',
                        font: { size: 11 }
                    },
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                },
                x: {
                    ticks: {
                        color: '#666',
                        font: { size: 11 }
                    },
                    grid: {
                        display: false
                    }
                }
            }
        }
    });
}

// Populate achievements list
function populateAchievements() {
    const achievementsList = document.getElementById('achievementsList');
    if (!achievementsList) return;

    achievementsList.innerHTML = '';

    // Show last 3 achievements
    const recentAchievements = userProgress.achievements.slice(-3);

    recentAchievements.forEach(achievement => {
        const achievementElement = document.createElement('div');
        achievementElement.className = 'achievement-item';
        achievementElement.innerHTML = `
            <div class="achievement-icon">
                <i class="${achievement.icon}"></i>
            </div>
            <div class="achievement-content">
                <h4>${achievement.title}</h4>
                <p>${achievement.description}</p>
                <small>${achievement.impact}</small>
            </div>
        `;

        achievementElement.addEventListener('click', () => {
            showAchievementModal(achievement);
        });

        achievementsList.appendChild(achievementElement);
    });
}

// Populate timeline preview
function populateTimelinePreview() {
    const timelinePreview = document.getElementById('timelinePreview');
    if (!timelinePreview) return;

    const upcomingMilestones = [
        {
            date: '15 Sty',
            title: 'Machine Learning Specialization',
            description: 'Rozpoczęcie kursu Andrew Ng na Coursera'
        },
        {
            date: '1 Lut',
            title: 'Portfolio Project #1',
            description: 'Computer Vision dla SMT Quality Control'
        },
        {
            date: '15 Lut',
            title: 'AWS ML Specialty',
            description: 'Przygotowania do egzaminu certyfikacyjnego'
        }
    ];

    timelinePreview.innerHTML = '';

    upcomingMilestones.forEach(milestone => {
        const milestoneElement = document.createElement('div');
        milestoneElement.className = 'timeline-item';
        milestoneElement.innerHTML = `
            <div class="timeline-date">${milestone.date}</div>
            <div class="timeline-content">
                <h4>${milestone.title}</h4>
                <p>${milestone.description}</p>
            </div>
        `;
        timelinePreview.appendChild(milestoneElement);
    });
}

// Action button functions
function startCourse() {
    console.log('🎓 Starting course...');

    addARIAMessage("Świetnie! Widzę, że chcesz rozpocząć naukę. Na podstawie Twojego profilu polecam zacząć od Machine Learning Specialization Andrew Ng. To idealny kurs dla inżynierów przechodzących do AI!");

    showQuickReplies([
        "Zapisz mnie na kurs",
        "Pokaż harmonogram",
        "Inne opcje"
    ]);

    // Navigate to courses section
    setTimeout(() => {
        document.querySelector('[href="#courses"]').click();
    }, 2000);

    showNotification('Przechodzę do sekcji kursów...', 'info');
}

function updateProgress() {
    console.log('📈 Updating progress...');

    // Simulate progress update
    userProgress.careerProgress = Math.min(userProgress.careerProgress + 5, 100);
    saveUserProgress();

    // Add new achievement
    const newAchievement = {
        id: Date.now(),
        title: "Postęp Zaktualizowany",
        description: "Ręczna aktualizacja postępu kariery",
        date: new Date().toISOString(),
        impact: "+5% Career Progress",
        icon: "fas fa-chart-line"
    };

    userProgress.achievements.push(newAchievement);
    saveUserProgress();

    // Refresh dashboard
    populateAchievements();
    createProgressGauge();

    addARIAMessage(`Doskonale! Zaktualizowałem Twój postęp do ${userProgress.careerProgress}%. Każdy krok przybliża Cię do celu! 🎯`);

    showQuickReplies([
        "Pokaż szczegóły",
        "Następne kroki",
        "Zaplanuj naukę"
    ]);

    showNotification('Postęp zaktualizowany!', 'success');

    // Show achievement modal
    setTimeout(() => {
        showAchievementModal(newAchievement);
    }, 1500);
}

function scheduleNetworking() {
    console.log('👥 Scheduling networking...');

    addARIAMessage("Networking to klucz do sukcesu w AI! Zaplanujmy Twoje uczestnictwo w wydarzeniach. Polecam zacząć od Global AI Bootcamp Kraków - to świetna okazja do poznania community!");

    showQuickReplies([
        "Zapisz na wydarzenie",
        "Przygotuj elevator pitch",
        "LinkedIn strategy"
    ]);

    // Navigate to networking section
    setTimeout(() => {
        document.querySelector('[href="#networking"]').click();
    }, 2000);

    showNotification('Przechodzę do sekcji networking...', 'info');
}

function reviewPortfolio() {
    console.log('📁 Reviewing portfolio...');

    addARIAMessage("Twoje portfolio to Twoja wizytówka! Z doświadczeniem w SMT/CBA i PVD masz unikalne możliwości. Stwórzmy projekty, które pokażą Twoją ekspertyzę w manufacturing + AI!");

    showQuickReplies([
        "Pomysły na projekty",
        "GitHub optimization",
        "Portfolio review"
    ]);

    // Navigate to portfolio section
    setTimeout(() => {
        document.querySelector('[href="#portfolio"]').click();
    }, 2000);

    showNotification('Przechodzę do sekcji portfolio...', 'info');
}

function viewAllAchievements() {
    console.log('🏆 Viewing all achievements...');

    addARIAMessage(`Masz już ${userProgress.achievements.length} osiągnięć! To świetny postęp. Każde osiągnięcie przybliża Cię do celu transformacji kariery. Które z nich było dla Ciebie najważniejsze?`);

    const achievementTitles = userProgress.achievements.map(a => a.title).slice(-4);
    showQuickReplies(achievementTitles);

    showNotification(`Masz ${userProgress.achievements.length} osiągnięć!`, 'success');
}

// Achievement modal functions
function showAchievementModal(achievement) {
    const modal = document.getElementById('achievementModal');
    const details = document.getElementById('achievementDetails');
    const impact = document.getElementById('achievementImpact');
    const nextSteps = document.getElementById('nextSteps');

    if (!modal || !details || !impact || !nextSteps) return;

    details.innerHTML = `
        <div class="achievement-showcase">
            <div class="achievement-icon-large">
                <i class="${achievement.icon}"></i>
            </div>
            <h3>${achievement.title}</h3>
            <p>${achievement.description}</p>
            <div class="achievement-date">
                Osiągnięte: ${new Date(achievement.date).toLocaleDateString('pl-PL')}
            </div>
        </div>
    `;

    impact.innerHTML = `
        <h4><i class="fas fa-chart-line"></i> Wpływ na Rozwój</h4>
        <div class="impact-item">
            <span class="impact-label">Bezpośredni wpływ:</span>
            <span class="impact-value">${achievement.impact}</span>
        </div>
        <div class="impact-item">
            <span class="impact-label">Postęp kariery:</span>
            <span class="impact-value">+${Math.floor(Math.random() * 3 + 2)}% ogólny postęp</span>
        </div>
        <div class="impact-item">
            <span class="impact-label">Wartość rynkowa:</span>
            <span class="impact-value">+€${Math.floor(Math.random() * 5000 + 2000)} potencjał zarobków</span>
        </div>
    `;

    nextSteps.innerHTML = `
        <h4><i class="fas fa-arrow-right"></i> Następne Kroki</h4>
        <ul class="next-steps-list">
            <li>Zaktualizuj profil LinkedIn z nowym osiągnięciem</li>
            <li>Dodaj projekt do portfolio GitHub</li>
            <li>Podziel się sukcesem w AI community</li>
            <li>Zaplanuj następny milestone</li>
        </ul>
    `;

    modal.style.display = 'block';
}

function closeAchievementModal() {
    const modal = document.getElementById('achievementModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

function continueWithARIA() {
    closeAchievementModal();
    addARIAMessage("Gratulacje ponownie! 🎉 Jestem dumny z Twojego postępu. Czy chcesz omówić następne kroki w Twojej transformacji kariery?");
    showQuickReplies([
        "Zaplanuj następny cel",
        "Aktualizuj LinkedIn",
        "Nowy projekt portfolio"
    ]);
}

// Update the initialization to include dashboard
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 AI Coach ARIA initializing...');

    initializeParticles();
    setupNavigation();
    initializeARIA();
    loadUserProgress();

    // Initialize dashboard after a short delay to ensure DOM is ready
    setTimeout(() => {
        initializeDashboard();
    }, 500);

    console.log('✅ ARIA platform ready!');
});

// Export new functions
window.startCourse = startCourse;
window.updateProgress = updateProgress;
window.scheduleNetworking = scheduleNetworking;
window.reviewPortfolio = reviewPortfolio;
window.viewAllAchievements = viewAllAchievements;
window.showAchievementModal = showAchievementModal;
window.closeAchievementModal = closeAchievementModal;
window.continueWithARIA = continueWithARIA;

// Step 3: Courses, Certifications and Portfolio Platforms - Updated with 2025 trends

// Premium certifications data
const premiumCertificationsData = [
    {
        id: 1,
        name: "AWS Certified Machine Learning - Specialty",
        provider: "Amazon Web Services",
        cost: 300,
        duration: "3-4 months prep",
        difficulty: "Advanced",
        roi: 4.0,
        salaryIncrease: "€15,000 - €25,000",
        validityPeriod: "3 years",
        prerequisites: ["AWS Cloud Practitioner", "Python experience"],
        examFormat: "Multiple choice, 180 minutes",
        passingScore: "750/1000",
        relevance: 96,
        status: "recommended",
        description: "Industry-leading ML certification with highest ROI for manufacturing professionals transitioning to AI.",
        keyTopics: ["ML Implementation", "Data Engineering", "Modeling", "Operations"],
        manufacturingValue: "Essential for implementing ML solutions in manufacturing environments",
        nextExamDate: "Available year-round",
        studyResources: ["AWS Training", "Practice Exams", "Hands-on Labs"]
    },
    {
        id: 2,
        name: "Microsoft Azure AI Engineer Associate",
        provider: "Microsoft",
        cost: 165,
        duration: "2-3 months prep",
        difficulty: "Intermediate",
        roi: 3.5,
        salaryIncrease: "€12,000 - €20,000",
        validityPeriod: "2 years",
        prerequisites: ["Azure Fundamentals", "Programming experience"],
        examFormat: "Multiple choice + labs, 150 minutes",
        passingScore: "700/1000",
        relevance: 92,
        status: "planned",
        description: "Microsoft's premier AI certification, excellent for enterprise environments.",
        keyTopics: ["Cognitive Services", "ML Solutions", "Computer Vision", "NLP"],
        manufacturingValue: "Perfect for integrating AI into existing Microsoft-based manufacturing systems",
        nextExamDate: "Available year-round",
        studyResources: ["Microsoft Learn", "Practice Labs", "Documentation"]
    },
    {
        id: 3,
        name: "Udacity AI for Manufacturing Nanodegree",
        provider: "Udacity",
        cost: 1600,
        duration: "4 months",
        difficulty: "Advanced",
        roi: 2.8,
        salaryIncrease: "€18,000 - €30,000",
        validityPeriod: "Lifetime",
        prerequisites: ["Python", "Statistics", "Manufacturing experience"],
        examFormat: "Project-based assessment",
        passingScore: "Pass/Fail based on projects",
        relevance: 98,
        status: "considering",
        description: "Specialized program combining AI with manufacturing expertise - perfect for your background.",
        keyTopics: ["Predictive Maintenance", "Quality Control AI", "Process Optimization", "Computer Vision"],
        manufacturingValue: "Directly applicable to SMT/CBA processes and PVD technology optimization",
        nextExamDate: "Monthly cohorts",
        studyResources: ["Mentor Support", "Real Projects", "Industry Partnerships"]
    },
    {
        id: 4,
        name: "Stanford AI Professional Certificate",
        provider: "Stanford University",
        cost: 4500,
        duration: "9 months",
        difficulty: "Expert",
        roi: 3.2,
        salaryIncrease: "€25,000 - €40,000",
        validityPeriod: "Lifetime",
        prerequisites: ["Advanced Math", "Programming", "ML Fundamentals"],
        examFormat: "Capstone project + presentations",
        passingScore: "B+ or higher",
        relevance: 94,
        status: "future-goal",
        description: "Prestigious certificate from world's leading AI university. Ultimate career accelerator.",
        keyTopics: ["Advanced ML", "AI Ethics", "Research Methods", "Innovation"],
        manufacturingValue: "Positions you as thought leader in AI-driven manufacturing transformation",
        nextExamDate: "September 2025 cohort",
        studyResources: ["Stanford Faculty", "Research Projects", "Industry Connections"]
    }
];

// Portfolio platforms data
const portfolioPlatformsData = [
    {
        id: 1,
        name: "GitHub Pages",
        type: "Code Repository + Website",
        cost: "Free",
        difficulty: "Beginner",
        setupTime: "2-3 hours",
        visibility: "Public",
        customDomain: "Yes (free)",
        relevance: 95,
        status: "priority",
        description: "Essential for showcasing code projects and building professional developer presence.",
        features: ["Version Control", "Project Showcase", "Professional Website", "Collaboration"],
        manufacturingProjects: [
            "SMT Quality Control Computer Vision",
            "PVD Process Optimization ML",
            "Manufacturing Data Pipeline",
            "Predictive Maintenance Dashboard"
        ],
        advantages: ["Industry Standard", "Free", "Professional", "SEO Friendly"],
        nextSteps: ["Create Account", "Setup Repository", "Build First Project", "Custom Domain"]
    },
    {
        id: 2,
        name: "Kaggle Notebooks",
        type: "Data Science Platform",
        cost: "Free",
        difficulty: "Intermediate",
        setupTime: "1 hour",
        visibility: "Public",
        customDomain: "No",
        relevance: 92,
        status: "recommended",
        description: "World's largest data science community. Perfect for showcasing ML and data analysis skills.",
        features: ["Competitions", "Datasets", "Community", "GPU Access"],
        manufacturingProjects: [
            "Manufacturing Defect Detection",
            "Process Parameter Optimization",
            "Quality Prediction Models",
            "Equipment Failure Analysis"
        ],
        advantages: ["Large Community", "Real Datasets", "Competitions", "Recognition"],
        nextSteps: ["Create Profile", "Join Competition", "Publish Notebook", "Build Reputation"]
    },
    {
        id: 3,
        name: "DataSciencePortfol.io",
        type: "Specialized Portfolio",
        cost: "$15/month",
        difficulty: "Beginner",
        setupTime: "4-5 hours",
        visibility: "Public",
        customDomain: "Yes",
        relevance: 88,
        status: "considering",
        description: "Specialized platform for data science professionals with beautiful templates.",
        features: ["Templates", "Project Showcase", "Blog", "Analytics"],
        manufacturingProjects: [
            "Manufacturing Analytics Dashboard",
            "AI-Driven Quality Control",
            "Process Improvement Case Studies",
            "Industry 4.0 Implementation"
        ],
        advantages: ["Professional Templates", "Easy Setup", "Analytics", "SEO Optimized"],
        nextSteps: ["Choose Template", "Upload Projects", "Write Case Studies", "Optimize SEO"]
    },
    {
        id: 4,
        name: "Personal Website (Custom)",
        type: "Professional Website",
        cost: "$50-100/year",
        difficulty: "Advanced",
        setupTime: "20-30 hours",
        visibility: "Public",
        customDomain: "Yes",
        relevance: 90,
        status: "future-goal",
        description: "Ultimate professional presence with complete control and customization.",
        features: ["Full Control", "Custom Design", "Blog", "Contact Forms"],
        manufacturingProjects: [
            "Complete Portfolio Showcase",
            "Technical Blog",
            "Case Study Library",
            "Professional Services"
        ],
        advantages: ["Complete Control", "Professional", "Scalable", "Personal Brand"],
        nextSteps: ["Domain Registration", "Hosting Setup", "Design Development", "Content Creation"]
    }
];

// Initialize sections data
function initializeSectionsData() {
    populateCoursesSection();
    populateCertificationsSection();
    populatePortfolioSection();
    console.log('📚 Sections data initialized');
}

// Populate courses section with enhanced 2025 data
function populateCoursesSection() {
    const coursesGrid = document.getElementById('coursesGrid');
    if (!coursesGrid) return;

    coursesGrid.innerHTML = `
        <div class="courses-overview">
            <h2>🎓 Top Coursera Certifications for 2025</h2>
            <p>Enhanced with latest AI trends and manufacturing applications</p>
            <div class="trends-highlight">
                <h3>🔥 Emerging Trends 2025:</h3>
                <div class="trends-grid">
                    ${courseraCoursesData.emergingTrends2025.map(trend => `
                        <div class="trend-item">
                            <h4>${trend.trend}</h4>
                            <div class="trend-growth">+${trend.growth}% growth</div>
                            <div class="trend-relevance">${trend.relevanceToAndrii}% relevance</div>
                        </div>
                    `).join('')}
                </div>
            </div>
        </div>

        <div class="courses-grid-enhanced">
            ${courseraCoursesData.topCertifications.map(course => `
                <div class="course-card-enhanced ${course.trending ? 'trending' : ''} ${course.new ? 'new' : ''}" data-course-id="${course.id}">
                    ${course.trending ? '<div class="trending-badge">🔥 TRENDING</div>' : ''}
                    ${course.new ? '<div class="new-badge">✨ NEW 2025</div>' : ''}

                    <div class="course-header-enhanced">
                        <h3>${course.title}</h3>
                        <div class="course-provider">
                            <span class="provider-name">${course.provider}</span>
                            <div class="course-rating">
                                <i class="fas fa-star"></i>
                                <span>${course.rating}</span>
                                <span class="enrollments">(${course.enrollments})</span>
                            </div>
                        </div>
                    </div>

                    <div class="course-metrics-enhanced">
                        <div class="metric-item">
                            <span class="metric-label">Duration:</span>
                            <span class="metric-value">${course.duration}</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-label">Cost:</span>
                            <span class="metric-value">${course.cost}</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-label">Difficulty:</span>
                            <span class="metric-value difficulty-${course.difficulty.toLowerCase().replace(' ', '-')}">${course.difficulty}</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-label">ROI Timeline:</span>
                            <span class="metric-value">${course.roiMonths} months</span>
                        </div>
                    </div>

                    <div class="course-relevance-enhanced">
                        <div class="relevance-header">
                            <span class="relevance-label">Relevance for Andrii:</span>
                            <span class="relevance-value">${course.relevance}%</span>
                        </div>
                        <div class="relevance-bar">
                            <div class="relevance-fill" style="width: ${course.relevance}%"></div>
                        </div>
                    </div>

                    <div class="course-roi">
                        <div class="roi-item">
                            <i class="fas fa-chart-line"></i>
                            <span class="roi-label">Salary Increase:</span>
                            <span class="roi-value">${course.salaryIncrease}</span>
                        </div>
                        <div class="roi-item">
                            <i class="fas fa-percentage"></i>
                            <span class="roi-label">Completion Rate:</span>
                            <span class="roi-value">${course.completionRate}%</span>
                        </div>
                    </div>

                    <div class="course-skills-enhanced">
                        <h4>Key Skills:</h4>
                        <div class="skills-container">
                            ${course.skills.map(skill => `<span class="skill-tag-enhanced">${skill}</span>`).join('')}
                        </div>
                    </div>

                    <div class="manufacturing-relevance-enhanced">
                        <h4><i class="fas fa-industry"></i> Manufacturing Application</h4>
                        <p>${course.manufacturingRelevance}</p>
                    </div>

                    <div class="course-actions-enhanced">
                        <button class="course-btn-enhanced primary" onclick="enrollInCourseEnhanced('${course.id}')">
                            <i class="fas fa-play"></i>
                            Start Learning
                        </button>
                        <button class="course-btn-enhanced secondary" onclick="viewCourseDetailsEnhanced('${course.id}')">
                            <i class="fas fa-info-circle"></i>
                            Full Details
                        </button>
                        <button class="course-btn-enhanced tertiary" onclick="addToLearningPath('${course.id}')">
                            <i class="fas fa-plus"></i>
                            Add to Path
                        </button>
                    </div>
                </div>
            `).join('')}
        </div>

        <div class="platform-comparison">
            <h3>📊 Platform Comparison 2025</h3>
            <div class="comparison-grid">
                ${Object.entries(courseraCoursesData.platformComparison).map(([platform, data]) => `
                    <div class="platform-card">
                        <h4>${platform.charAt(0).toUpperCase() + platform.slice(1)}</h4>
                        <div class="platform-rating">
                            <i class="fas fa-star"></i>
                            <span>${data.rating}</span>
                        </div>
                        <div class="platform-courses">
                            <span>${data.manufacturingCourses} manufacturing courses</span>
                        </div>
                        <div class="platform-best-for">
                            <strong>Best for:</strong> ${data.bestFor}
                        </div>
                        <div class="platform-strengths">
                            <strong>Strengths:</strong>
                            <ul>
                                ${data.strengths.map(strength => `<li>${strength}</li>`).join('')}
                            </ul>
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
}

// Populate certifications section
function populateCertificationsSection() {
    const certificationsGrid = document.getElementById('certificationsGrid');
    if (!certificationsGrid) return;

    certificationsGrid.innerHTML = premiumCertificationsData.map(cert => `
        <div class="certification-card ${cert.status}" data-cert-id="${cert.id}">
            <div class="cert-header">
                <div class="cert-provider-logo">
                    <i class="fab fa-${getProviderIcon(cert.provider)}"></i>
                </div>
                <div class="cert-info">
                    <h3>${cert.name}</h3>
                    <p class="cert-provider">${cert.provider}</p>
                </div>
                <div class="cert-cost">
                    <span class="cost-label">Cost:</span>
                    <span class="cost-value">$${cert.cost}</span>
                </div>
            </div>

            <div class="cert-metrics">
                <div class="metric">
                    <span class="metric-label">ROI:</span>
                    <span class="metric-value roi-${cert.roi >= 3.5 ? 'high' : cert.roi >= 3 ? 'medium' : 'low'}">${cert.roi}x</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Salary Increase:</span>
                    <span class="metric-value">${cert.salaryIncrease}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Prep Time:</span>
                    <span class="metric-value">${cert.duration}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Difficulty:</span>
                    <span class="metric-value difficulty-${cert.difficulty.toLowerCase()}">${cert.difficulty}</span>
                </div>
            </div>

            <div class="cert-description">
                <p>${cert.description}</p>
            </div>

            <div class="manufacturing-value">
                <h4><i class="fas fa-industry"></i> Manufacturing Value</h4>
                <p>${cert.manufacturingValue}</p>
            </div>

            <div class="cert-topics">
                <h4>Key Topics:</h4>
                <div class="topics-list">
                    ${cert.keyTopics.map(topic => `<span class="topic-tag">${topic}</span>`).join('')}
                </div>
            </div>

            <div class="cert-actions">
                <button class="cert-btn primary" onclick="startCertPrep(${cert.id})">
                    <i class="fas fa-play"></i>
                    Start Preparation
                </button>
                <button class="cert-btn secondary" onclick="viewCertDetails(${cert.id})">
                    <i class="fas fa-info-circle"></i>
                    Full Details
                </button>
                <button class="cert-btn tertiary" onclick="compareCertifications(${cert.id})">
                    <i class="fas fa-balance-scale"></i>
                    Compare
                </button>
            </div>
        </div>
    `).join('');
}

// Populate portfolio section
function populatePortfolioSection() {
    const portfolioGrid = document.getElementById('portfolioGrid');
    if (!portfolioGrid) return;

    portfolioGrid.innerHTML = portfolioPlatformsData.map(platform => `
        <div class="portfolio-card ${platform.status}" data-platform-id="${platform.id}">
            <div class="platform-header">
                <div class="platform-icon">
                    <i class="fab fa-${getPlatformIcon(platform.name)}"></i>
                </div>
                <div class="platform-info">
                    <h3>${platform.name}</h3>
                    <p class="platform-type">${platform.type}</p>
                </div>
                <div class="platform-cost ${platform.cost === 'Free' ? 'free' : 'paid'}">
                    ${platform.cost}
                </div>
            </div>

            <div class="platform-stats">
                <div class="stat">
                    <span class="stat-label">Setup Time:</span>
                    <span class="stat-value">${platform.setupTime}</span>
                </div>
                <div class="stat">
                    <span class="stat-label">Difficulty:</span>
                    <span class="stat-value difficulty-${platform.difficulty.toLowerCase()}">${platform.difficulty}</span>
                </div>
                <div class="stat">
                    <span class="stat-label">Relevance:</span>
                    <span class="stat-value">${platform.relevance}%</span>
                </div>
            </div>

            <div class="platform-description">
                <p>${platform.description}</p>
            </div>

            <div class="manufacturing-projects">
                <h4><i class="fas fa-project-diagram"></i> Manufacturing Projects</h4>
                <ul class="projects-list">
                    ${platform.manufacturingProjects.map(project => `<li>${project}</li>`).join('')}
                </ul>
            </div>

            <div class="platform-advantages">
                <h4>Key Advantages:</h4>
                <div class="advantages-list">
                    ${platform.advantages.map(advantage => `<span class="advantage-tag">${advantage}</span>`).join('')}
                </div>
            </div>

            <div class="platform-actions">
                <button class="platform-btn primary" onclick="createAccount(${platform.id})">
                    <i class="fas fa-user-plus"></i>
                    Create Account
                </button>
                <button class="platform-btn secondary" onclick="startProject(${platform.id})">
                    <i class="fas fa-rocket"></i>
                    Start Project
                </button>
                <button class="platform-btn tertiary" onclick="viewExamples(${platform.id})">
                    <i class="fas fa-eye"></i>
                    View Examples
                </button>
            </div>
        </div>
    `).join('');
}

// Helper functions
function getStatusLabel(status) {
    const labels = {
        'recommended': 'Polecany',
        'in-progress': 'W trakcie',
        'planned': 'Zaplanowany',
        'available': 'Dostępny',
        'considering': 'Rozważany',
        'future-goal': 'Przyszły cel',
        'priority': 'Priorytet'
    };
    return labels[status] || status;
}

function getActionIcon(status) {
    const icons = {
        'recommended': 'fa-play',
        'in-progress': 'fa-continue',
        'planned': 'fa-calendar-plus',
        'available': 'fa-play',
        'considering': 'fa-eye',
        'future-goal': 'fa-bookmark',
        'priority': 'fa-rocket'
    };
    return icons[status] || 'fa-play';
}

function getActionLabel(status) {
    const labels = {
        'recommended': 'Rozpocznij',
        'in-progress': 'Kontynuuj',
        'planned': 'Zaplanuj',
        'available': 'Zapisz się',
        'considering': 'Sprawdź',
        'future-goal': 'Dodaj do celów',
        'priority': 'Rozpocznij teraz'
    };
    return labels[status] || 'Rozpocznij';
}

function getProviderIcon(provider) {
    const icons = {
        'Amazon Web Services': 'aws',
        'Microsoft': 'microsoft',
        'Udacity': 'graduation-cap',
        'Stanford University': 'university'
    };
    return icons[provider] || 'certificate';
}

function getPlatformIcon(platform) {
    const icons = {
        'GitHub Pages': 'github',
        'Kaggle Notebooks': 'kaggle',
        'DataSciencePortfol.io': 'chart-bar',
        'Personal Website (Custom)': 'globe'
    };
    return icons[platform] || 'laptop-code';
}

function formatNumber(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(0) + 'k';
    }
    return num.toString();
}

// Update initialization to include sections
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 AI Coach ARIA initializing...');

    initializeParticles();
    setupNavigation();
    initializeARIA();
    loadUserProgress();

    // Initialize dashboard and sections after a short delay
    setTimeout(() => {
        initializeDashboard();
        initializeSectionsData();
    }, 500);

    console.log('✅ ARIA platform ready!');
});

// Step 3: Button handlers for courses, certifications and portfolio
// Course action handlers
function enrollInCourse(courseId) {
    const course = courseraCoursesData.find(c => c.id === courseId);
    if (!course) return;

    console.log(`📚 Enrolling in course: ${course.title}`);

    // Update course status
    course.status = 'in-progress';
    userProgress.completedCourses++;
    saveUserProgress();

    // Add achievement
    const achievement = {
        id: Date.now(),
        title: `Enrolled in ${course.title}`,
        description: `Started learning with ${course.instructor}`,
        date: new Date().toISOString(),
        impact: `+${Math.floor(Math.random() * 5 + 3)}% ${course.skills[0]} Skills`,
        icon: "fas fa-graduation-cap"
    };

    userProgress.achievements.push(achievement);
    saveUserProgress();

    // ARIA response
    addARIAMessage(`Świetnie! Zapisałem Cię na "${course.title}" z ${course.instructor}. To doskonały wybór dla Twojej transformacji! Kurs ma ${course.relevance}% relevance dla Twojego profilu. Czy chcesz, żebym pomógł Ci zaplanować harmonogram nauki?`);

    showQuickReplies([
        "Zaplanuj harmonogram",
        "Pokaż materiały",
        "Ustaw przypomnienia",
        "Inne kursy"
    ]);

    // Refresh the courses section
    populateCoursesSection();

    showNotification(`Zapisano na kurs: ${course.title}`, 'success');

    // Show achievement modal
    setTimeout(() => {
        showAchievementModal(achievement);
    }, 1500);
}

function viewCourseDetails(courseId) {
    const course = courseraCoursesData.find(c => c.id === courseId);
    if (!course) return;

    console.log(`📖 Viewing details for: ${course.title}`);

    addARIAMessage(`Oto szczegóły kursu "${course.title}":

📊 **Statystyki:**
- Instruktor: ${course.instructor} (${course.university})
- Ocena: ${course.rating}/5 (${formatNumber(course.enrolled)} uczestników)
- Czas trwania: ${course.duration}
- Poziom: ${course.level}
- Cena: ${course.price}

🎯 **Relevance dla Ciebie: ${course.relevance}%**

🔧 **Manufacturing Relevance:**
${course.manufacturingRelevance}

📚 **Umiejętności:** ${course.skills.join(', ')}

🚀 **Projekty:** ${course.projects.join(', ')}

Czy chcesz się zapisać na ten kurs?`);

    showQuickReplies([
        "Zapisz mnie",
        "Porównaj z innymi",
        "Zaplanuj na później",
        "Więcej informacji"
    ]);

    showNotification('Szczegóły kursu wyświetlone w czacie ARIA', 'info');
}

function scheduleCourse(courseId) {
    const course = courseraCoursesData.find(c => c.id === courseId);
    if (!course) return;

    console.log(`📅 Scheduling course: ${course.title}`);

    course.status = 'planned';
    populateCoursesSection();

    addARIAMessage(`Zaplanowałem kurs "${course.title}" na ${course.nextSession}.

📅 **Harmonogram:**
- Start: ${course.nextSession}
- Czas ukończenia: ${course.timeToComplete}
- Intensywność: ${Math.ceil(parseInt(course.duration) / 4)} godzin/tydzień

Czy chcesz, żebym ustawił przypomnienia i przygotował plan nauki?`);

    showQuickReplies([
        "Ustaw przypomnienia",
        "Przygotuj plan nauki",
        "Dodaj do kalendarza",
        "Zmień datę"
    ]);

    showNotification(`Kurs zaplanowany na ${course.nextSession}`, 'success');
}

// Certification action handlers
function startCertPrep(certId) {
    const cert = premiumCertificationsData.find(c => c.id === certId);
    if (!cert) return;

    console.log(`🎓 Starting certification prep: ${cert.name}`);

    cert.status = 'in-progress';
    populateCertificationsSection();

    addARIAMessage(`Rozpoczynamy przygotowania do ${cert.name}!

💰 **Inwestycja:** $${cert.cost} (ROI: ${cert.roi}x)
📈 **Potencjalny wzrost wynagrodzeń:** ${cert.salaryIncrease}
⏱️ **Czas przygotowań:** ${cert.duration}
🎯 **Poziom trudności:** ${cert.difficulty}

**Plan przygotowań:**
1. ${cert.studyResources[0]}
2. ${cert.studyResources[1]}
3. ${cert.studyResources[2]}

**Manufacturing Value:** ${cert.manufacturingValue}

Czy chcesz, żebym stworzył szczegółowy plan nauki?`);

    showQuickReplies([
        "Stwórz plan nauki",
        "Pokaż materiały",
        "Zaplanuj egzamin",
        "Porównaj z innymi"
    ]);

    showNotification(`Rozpoczęto przygotowania: ${cert.name}`, 'success');
}

function viewCertDetails(certId) {
    const cert = premiumCertificationsData.find(c => c.id === certId);
    if (!cert) return;

    console.log(`📋 Viewing certification details: ${cert.name}`);

    addARIAMessage(`Szczegółowe informacje o ${cert.name}:

🏢 **Provider:** ${cert.provider}
💰 **Koszt:** $${cert.cost}
📊 **ROI:** ${cert.roi}x (${cert.salaryIncrease})
⏰ **Przygotowania:** ${cert.duration}
🎯 **Trudność:** ${cert.difficulty}
📅 **Ważność:** ${cert.validityPeriod}

**Wymagania wstępne:**
${cert.prerequisites.map(req => `• ${req}`).join('\n')}

**Format egzaminu:**
${cert.examFormat}
Wynik zaliczający: ${cert.passingScore}

**Kluczowe tematy:**
${cert.keyTopics.map(topic => `• ${topic}`).join('\n')}

**Wartość dla manufacturing:**
${cert.manufacturingValue}

**Następny termin egzaminu:** ${cert.nextExamDate}

Czy chcesz rozpocząć przygotowania?`);

    showQuickReplies([
        "Rozpocznij przygotowania",
        "Zaplanuj egzamin",
        "Porównaj certyfikacje",
        "Sprawdź wymagania"
    ]);

    showNotification('Szczegóły certyfikacji w czacie ARIA', 'info');
}

function compareCertifications(certId) {
    const cert = premiumCertificationsData.find(c => c.id === certId);
    if (!cert) return;

    console.log(`⚖️ Comparing certifications with: ${cert.name}`);

    // Find top 3 certifications by ROI
    const topCerts = premiumCertificationsData
        .sort((a, b) => b.roi - a.roi)
        .slice(0, 3);

    addARIAMessage(`Porównanie certyfikacji dla Twojego profilu:

**${cert.name}** (wybrana):
• ROI: ${cert.roi}x | Koszt: $${cert.cost} | Trudność: ${cert.difficulty}
• Wzrost wynagrodzeń: ${cert.salaryIncrease}

**Porównanie z top certyfikacjami:**

${topCerts.map((c, index) => `
${index + 1}. **${c.name}**
• ROI: ${c.roi}x | Koszt: $${c.cost} | Trudność: ${c.difficulty}
• Wzrost: ${c.salaryIncrease}
• Relevance: ${c.relevance}%`).join('\n')}

**Rekomendacja ARIA:**
Na podstawie Twojego profilu w manufacturing i celów w AI, ${topCerts[0].name} ma najlepszy ROI (${topCerts[0].roi}x) i jest idealny dla Twojej transformacji kariery.

Którą certyfikację chcesz wybrać?`);

    showQuickReplies([
        topCerts[0].name.split(' ')[0],
        topCerts[1].name.split(' ')[0],
        topCerts[2].name.split(' ')[0],
        "Więcej informacji"
    ]);

    showNotification('Porównanie certyfikacji w czacie ARIA', 'info');
}

// Portfolio platform action handlers
function createAccount(platformId) {
    const platform = portfolioPlatformsData.find(p => p.id === platformId);
    if (!platform) return;

    console.log(`👤 Creating account on: ${platform.name}`);

    platform.status = 'in-progress';
    populatePortfolioSection();

    addARIAMessage(`Świetnie! Tworzymy konto na ${platform.name}!

🎯 **Platforma:** ${platform.type}
💰 **Koszt:** ${platform.cost}
⏱️ **Czas setup:** ${platform.setupTime}
🎨 **Trudność:** ${platform.difficulty}
📊 **Relevance:** ${platform.relevance}%

**Zalety:**
${platform.advantages.map(adv => `• ${adv}`).join('\n')}

**Projekty manufacturing dla Twojego portfolio:**
${platform.manufacturingProjects.map(proj => `• ${proj}`).join('\n')}

**Następne kroki:**
${platform.nextSteps.map((step, index) => `${index + 1}. ${step}`).join('\n')}

Czy chcesz, żebym pomógł Ci z pierwszym projektem?`);

    showQuickReplies([
        "Pomóż z pierwszym projektem",
        "Pokaż przykłady",
        "Setup instrukcje",
        "Inne platformy"
    ]);

    showNotification(`Konto tworzone: ${platform.name}`, 'success');
}

function startProject(platformId) {
    const platform = portfolioPlatformsData.find(p => p.id === platformId);
    if (!platform) return;

    console.log(`🚀 Starting project on: ${platform.name}`);

    const projectSuggestions = [
        {
            title: "SMT Quality Control Computer Vision",
            description: "AI system for automated defect detection in SMT assembly",
            technologies: ["Python", "OpenCV", "TensorFlow", "Flask"],
            impact: "60% reduction in false positives",
            timeline: "4-6 weeks"
        },
        {
            title: "PVD Process Optimization ML",
            description: "Machine learning model for optimizing PVD coating parameters",
            technologies: ["Python", "Scikit-learn", "Pandas", "Matplotlib"],
            impact: "15% improvement in coating quality",
            timeline: "3-4 weeks"
        },
        {
            title: "Manufacturing Data Pipeline",
            description: "Automated data pipeline for real-time manufacturing analytics",
            technologies: ["Python", "Apache Airflow", "PostgreSQL", "Grafana"],
            impact: "Real-time KPI monitoring",
            timeline: "5-7 weeks"
        }
    ];

    const randomProject = projectSuggestions[Math.floor(Math.random() * projectSuggestions.length)];

    addARIAMessage(`Rozpoczynamy projekt na ${platform.name}!

🎯 **Polecany projekt:** ${randomProject.title}

📝 **Opis:** ${randomProject.description}

🛠️ **Technologie:** ${randomProject.technologies.join(', ')}

📈 **Oczekiwany impact:** ${randomProject.impact}

⏰ **Timeline:** ${randomProject.timeline}

**Dlaczego ten projekt?**
• Wykorzystuje Twoje doświadczenie w ${randomProject.title.includes('SMT') ? 'SMT/CBA' : randomProject.title.includes('PVD') ? 'PVD technology' : 'manufacturing'}
• Pokazuje umiejętności AI/ML
• Ma realny business impact
• Idealny dla portfolio

Czy chcesz rozpocząć ten projekt?`);

    showQuickReplies([
        "Rozpocznij projekt",
        "Inne pomysły",
        "Pokaż template",
        "Pomoc z setup"
    ]);

    showNotification(`Projekt rozpoczęty: ${randomProject.title}`, 'success');
}

function viewExamples(platformId) {
    const platform = portfolioPlatformsData.find(p => p.id === platformId);
    if (!platform) return;

    console.log(`👁️ Viewing examples for: ${platform.name}`);

    const examples = {
        1: [ // GitHub Pages
            "https://github.com/manufacturing-ai/smt-quality-control",
            "https://github.com/process-optimization/pvd-ml-models",
            "https://github.com/industry40/predictive-maintenance"
        ],
        2: [ // Kaggle
            "Manufacturing Defect Detection Competition",
            "Process Parameter Optimization Challenge",
            "Quality Prediction Models Showcase"
        ],
        3: [ // DataSciencePortfol.io
            "Manufacturing Analytics Dashboard",
            "AI-Driven Quality Control Case Study",
            "Industry 4.0 Implementation Portfolio"
        ],
        4: [ // Personal Website
            "Complete Portfolio Showcase",
            "Technical Blog with Case Studies",
            "Professional Services Landing Page"
        ]
    };

    addARIAMessage(`Przykłady portfolio na ${platform.name}:

${examples[platformId].map((example, index) => `${index + 1}. ${example}`).join('\n')}

**Dlaczego te przykłady są dobre:**
• Pokazują real-world manufacturing problems
• Demonstrują AI/ML solutions
• Mają measurable business impact
• Są relevant dla Twojego background

**Kluczowe elementy successful portfolio:**
• Clear problem statement
• Technical solution approach
• Quantified results
• Professional presentation
• Manufacturing domain expertise

Czy chcesz, żebym pomógł Ci stworzyć podobny projekt?`);

    showQuickReplies([
        "Stwórz podobny projekt",
        "Analizuj przykłady",
        "Template projektu",
        "Inne platformy"
    ]);

    showNotification(`Przykłady wyświetlone dla ${platform.name}`, 'info');
}

// Export new functions
window.enrollInCourse = enrollInCourse;
window.viewCourseDetails = viewCourseDetails;
window.scheduleCourse = scheduleCourse;
window.startCertPrep = startCertPrep;
window.viewCertDetails = viewCertDetails;
window.compareCertifications = compareCertifications;
window.createAccount = createAccount;
window.startProject = startProject;
window.viewExamples = viewExamples;

// Step 4: Networking Events, Career Roadmap and Voice Interface
// Polish AI Events data specifically for Andrii's networking strategy
const polishAIEventsData = [
    {
        id: 1,
        name: "Global AI Bootcamp Kraków 2025",
        date: "2025-03-07",
        location: "Kraków, Technopark",
        type: "Conference",
        format: "Hybrid",
        attendees: 500,
        cost: "Free",
        relevance: 96,
        status: "recommended",
        description: "Największe wydarzenie AI w Polsce. Idealne dla networking z liderami branży i poznania najnowszych trendów w AI.",
        keyTopics: ["Machine Learning", "AI in Manufacturing", "Industry 4.0", "Computer Vision"],
        speakers: ["Dr. Piotr Sankowski (Google)", "Prof. Henryk Sienkiewicz (AGH)", "Anna Kowalska (Microsoft AI)"],
        manufacturingFocus: "Dedykowane sesje o AI w przemyśle, automatyzacji i Industry 4.0",
        networkingValue: "Spotkanie z 50+ ekspertami AI, potencjalni mentorzy i współpracownicy",
        registrationDeadline: "2025-02-15",
        website: "https://globalaibootcamp.com/krakow",
        organizer: "AI Poland Community"
    },
    {
        id: 2,
        name: "Trójmiasto AI Meetup",
        date: "2025-01-25",
        location: "Gdańsk, Olivia Business Centre",
        type: "Meetup",
        format: "In-person",
        attendees: 80,
        cost: "Free",
        relevance: 94,
        status: "priority",
        description: "Miesięczne spotkania AI community w Trójmieście. Blisko Twojej lokalizacji w Gdańsku!",
        keyTopics: ["Data Science", "ML Engineering", "AI Startups", "Career Development"],
        speakers: ["Local AI practitioners", "Startup founders", "Tech leads"],
        manufacturingFocus: "Regularne prezentacje o AI w przemyśle morskim i automotive",
        networkingValue: "Lokalna społeczność, potencjalne job opportunities w regionie",
        registrationDeadline: "2025-01-20",
        website: "https://meetup.com/trojmiasto-ai",
        organizer: "Trójmiasto Tech Community"
    },
    {
        id: 3,
        name: "Data Science Summit Warsaw",
        date: "2025-04-15",
        location: "Warszawa, Palace of Culture",
        type: "Summit",
        format: "In-person",
        attendees: 800,
        cost: "€150 (Early bird: €99)",
        relevance: 92,
        status: "planned",
        description: "Największa konferencja Data Science w Polsce. Premium networking z top ekspertami.",
        keyTopics: ["Advanced Analytics", "MLOps", "AI Ethics", "Business Intelligence"],
        speakers: ["International DS leaders", "Polish AI pioneers", "Industry executives"],
        manufacturingFocus: "Track dedykowany AI w przemyśle i automatyzacji procesów",
        networkingValue: "800+ uczestników, premium networking dinner, job fair",
        registrationDeadline: "2025-03-01",
        website: "https://datasciencesummit.pl",
        organizer: "Data Science Poland"
    },
    {
        id: 4,
        name: "AI in Manufacturing Conference",
        date: "2025-05-20",
        location: "Katowice, Spodek",
        type: "Industry Conference",
        format: "Hybrid",
        attendees: 300,
        cost: "€200",
        relevance: 98,
        status: "must-attend",
        description: "Specjalistyczna konferencja AI w przemyśle. Idealnie dopasowana do Twojego profilu!",
        keyTopics: ["Predictive Maintenance", "Quality Control AI", "Smart Manufacturing", "Digital Twins"],
        speakers: ["Siemens AI team", "Bosch Industry 4.0", "ABB Robotics", "Local manufacturing leaders"],
        manufacturingFocus: "100% focus na AI w przemyśle - case studies, ROI analysis, implementation",
        networkingValue: "Bezpośredni kontakt z decision makers w manufacturing companies",
        registrationDeadline: "2025-04-15",
        website: "https://aimanufacturing.pl",
        organizer: "Polish Manufacturing Association"
    },
    {
        id: 5,
        name: "PyData Warsaw",
        date: "2025-02-28",
        location: "Warszawa, Google Campus",
        type: "Technical Meetup",
        format: "In-person",
        attendees: 120,
        cost: "Free",
        relevance: 89,
        status: "considering",
        description: "Techniczne spotkania Python community. Doskonałe dla pogłębienia umiejętności programistycznych.",
        keyTopics: ["Python for Data Science", "Pandas", "Scikit-learn", "TensorFlow"],
        speakers: ["Python core developers", "Data scientists", "ML engineers"],
        manufacturingFocus: "Prezentacje o Python w aplikacjach przemysłowych i IoT",
        networkingValue: "Technical community, potential collaborators for open-source projects",
        registrationDeadline: "2025-02-25",
        website: "https://pydata.org/warsaw",
        organizer: "PyData Community",
        manusAnalysis: {
            strategicValue: 'High technical depth, Python skills essential for AI career',
            networkingPotential: 'Technical community, open-source collaboration opportunities',
            careerImpact: 'Skill development focused, less direct career advancement',
            preparationNeeds: ['Python portfolio projects', 'Technical presentation skills'],
            followUpActions: ['Contribute to open-source projects', 'Technical blog posts']
        }
    },
    {
        id: 6,
        name: "Women in AI Poland",
        date: "2025-03-15",
        location: "Online + Local chapters",
        type: "Diversity Event",
        format: "Hybrid",
        attendees: 200,
        cost: "Free",
        relevance: 85,
        status: "supporting",
        description: "Wspieranie różnorodności w AI. Doskonała okazja do networking i mentoring.",
        keyTopics: ["AI Ethics", "Inclusive AI", "Career Development", "Leadership"],
        speakers: ["Female AI leaders", "Diversity advocates", "Tech executives"],
        manufacturingFocus: "Sesje o kobietach w tech i manufacturing leadership",
        networkingValue: "Diverse perspectives, mentoring opportunities, inclusive community",
        registrationDeadline: "2025-03-10",
        website: "https://womeninai.pl",
        organizer: "Women in AI Poland",
        manusAnalysis: {
            strategicValue: 'Diversity and inclusion perspective, leadership development',
            networkingPotential: 'Mentoring opportunities, diverse professional network',
            careerImpact: 'Leadership skills, inclusive mindset for team management',
            preparationNeeds: ['Leadership examples from manufacturing', 'Diversity initiatives'],
            followUpActions: ['Mentoring others', 'Diversity advocacy in workplace']
        }
    },
    {
        id: 7,
        name: "NVIDIA GTC Europe 2025",
        date: "2025-09-15",
        location: "Amsterdam, Netherlands",
        type: "International Conference",
        format: "Hybrid",
        attendees: 2000,
        cost: "€400 (Virtual: €150)",
        relevance: 93,
        status: "recommended",
        description: "Największa konferencja AI w Europie. NVIDIA prezentuje najnowsze technologie AI i GPU computing.",
        keyTopics: ["GPU Computing", "Deep Learning", "Edge AI", "Autonomous Systems"],
        speakers: ["Jensen Huang (NVIDIA CEO)", "European AI leaders", "Industry pioneers"],
        manufacturingFocus: "Omniverse for manufacturing, digital twins, robotics AI",
        networkingValue: "Global network, cutting-edge technology exposure, industry partnerships",
        registrationDeadline: "2025-08-01",
        website: "https://nvidia.com/gtc",
        organizer: "NVIDIA",
        manusAnalysis: {
            strategicValue: 'Cutting-edge technology exposure, global perspective on AI trends',
            networkingPotential: 'International contacts, technology partnerships, startup ecosystem',
            careerImpact: 'Positions as early adopter of latest AI technologies',
            preparationNeeds: ['NVIDIA technology familiarity', 'Manufacturing use cases for GPU'],
            followUpActions: ['NVIDIA certification pursuit', 'Technology implementation at Aptiv']
        }
    },
    {
        id: 8,
        name: "European Manufacturing AI Summit",
        date: "2025-06-10",
        location: "Munich, Germany",
        type: "Industry Summit",
        format: "In-person",
        attendees: 600,
        cost: "€350",
        relevance: 97,
        status: "must-attend",
        description: "Premier European event for AI in manufacturing. Direct relevance to automotive industry.",
        keyTopics: ["Smart Manufacturing", "Predictive Maintenance", "Quality 4.0", "Supply Chain AI"],
        speakers: ["BMW AI team", "Mercedes-Benz innovation", "Siemens Digital Industries", "Bosch AI"],
        manufacturingFocus: "100% manufacturing focus - automotive, electronics, process industries",
        networkingValue: "Direct access to European automotive AI leaders and decision makers",
        registrationDeadline: "2025-05-01",
        website: "https://manufacturingai-summit.eu",
        organizer: "European Manufacturing Association",
        manusAnalysis: {
            strategicValue: 'Perfect alignment with career goals, automotive industry focus',
            networkingPotential: 'European automotive contacts, potential job opportunities',
            careerImpact: 'Positions as expert in automotive AI, international recognition',
            preparationNeeds: ['Automotive AI case studies', 'European market knowledge'],
            followUpActions: ['European job market exploration', 'International collaboration']
        }
    },
    {
        id: 9,
        name: "AI & Robotics Expo Poland",
        date: "2025-10-08",
        location: "Poznań, MTP",
        type: "Trade Show",
        format: "In-person",
        attendees: 1200,
        cost: "€80",
        relevance: 91,
        status: "planned",
        description: "Największe targi AI i robotyki w Polsce. Połączenie technologii, biznesu i networking.",
        keyTopics: ["Industrial Robotics", "AI Applications", "Automation", "Industry 4.0"],
        speakers: ["Polish tech leaders", "International vendors", "Startup founders"],
        manufacturingFocus: "Industrial automation, collaborative robots, smart factory solutions",
        networkingValue: "Business contacts, vendor relationships, technology partnerships",
        registrationDeadline: "2025-09-15",
        website: "https://airoboticsexpo.pl",
        organizer: "Polish Tech Expo",
        manusAnalysis: {
            strategicValue: 'Business and technology combination, vendor ecosystem exposure',
            networkingPotential: 'Business development contacts, technology partnerships',
            careerImpact: 'Understanding of AI business ecosystem, potential consulting opportunities',
            preparationNeeds: ['Business case development', 'ROI analysis skills'],
            followUpActions: ['Vendor partnerships', 'Business development opportunities']
        }
    },
    {
        id: 10,
        name: "Baltic AI Conference",
        date: "2025-11-20",
        location: "Gdańsk, European Solidarity Centre",
        type: "Regional Conference",
        format: "Hybrid",
        attendees: 400,
        cost: "€120",
        relevance: 95,
        status: "priority",
        description: "Regionalna konferencja AI dla krajów bałtyckich. Lokalna w Gdańsku - idealna dla Andrii!",
        keyTopics: ["Regional AI Development", "Cross-border Collaboration", "Maritime AI", "Smart Cities"],
        speakers: ["Baltic AI researchers", "Regional government officials", "Local tech leaders"],
        manufacturingFocus: "Maritime industry AI, port automation, regional manufacturing",
        networkingValue: "Regional network building, local government contacts, maritime industry",
        registrationDeadline: "2025-10-30",
        website: "https://balticai.org",
        organizer: "Baltic AI Initiative",
        manusAnalysis: {
            strategicValue: 'Regional leadership opportunity, local market expertise',
            networkingPotential: 'Regional influence, government contacts, maritime industry',
            careerImpact: 'Positions as regional AI expert, potential thought leadership',
            preparationNeeds: ['Regional market analysis', 'Maritime industry knowledge'],
            followUpActions: ['Regional AI community leadership', 'Government collaboration']
        }
    }
];

// Enhanced networking strategy with Manus AI insights
const networkingStrategy = {
    manusRecommendations: {
        priorityEvents: [
            { id: 4, reason: 'Perfect alignment with manufacturing background and AI goals' },
            { id: 2, reason: 'Local networking in Gdańsk, regular engagement opportunity' },
            { id: 8, reason: 'European automotive industry connections' },
            { id: 10, reason: 'Regional leadership and local market positioning' }
        ],
        networkingGoals: {
            shortTerm: ['Build local AI community presence', 'Establish 20+ meaningful connections', 'Find 2-3 potential mentors'],
            mediumTerm: ['Speak at 1-2 events', 'Establish thought leadership', 'Build international network'],
            longTerm: ['Become recognized expert in manufacturing AI', 'Advisory roles', 'Conference organizing committee']
        },
        elevatorPitchVersions: {
            technical: "I'm Andrii, a Process Engineer at Aptiv with 15+ years in manufacturing, specializing in SMT/CBA and PVD coating. I'm transforming my career toward AI/Data Science, focusing on computer vision for quality control and predictive maintenance. My unique combination of deep manufacturing domain knowledge and emerging AI skills positions me to bridge traditional manufacturing with Industry 4.0.",
            business: "I help manufacturing companies implement AI solutions that deliver real ROI. With 15+ years at Aptiv in process engineering and a PhD in Materials, I understand both the technical challenges and business needs. I'm currently developing AI applications for quality control and predictive maintenance that can reduce defects by 60% and improve OEE by 25%.",
            networking: "Hi, I'm Andrii from Gdańsk. I'm a manufacturing engineer transitioning to AI, combining 15 years of automotive experience with machine learning. I'm passionate about applying AI to solve real manufacturing problems - from computer vision quality control to predictive maintenance. I'm looking to connect with others working at the intersection of manufacturing and AI."
        },
        followUpStrategy: {
            immediate: ['LinkedIn connection within 24 hours', 'Personalized message referencing conversation'],
            weekly: ['Share relevant content', 'Comment on their posts', 'Offer value or insights'],
            monthly: ['Schedule coffee/video call', 'Propose collaboration', 'Share progress updates']
        }
    }
};

// Career roadmap data - 12-month transformation plan
const careerRoadmapData = [
    {
        id: 1,
        phase: "Foundation",
        month: 1,
        title: "Python & Data Science Fundamentals",
        date: "January 2025",
        status: "current",
        description: "Solidne podstawy programowania i analizy danych",
        goals: [
            "Ukończenie Python for Data Analysis course",
            "Setup GitHub portfolio",
            "Pierwszy projekt: SMT Data Analysis"
        ],
        milestones: [
            "Python proficiency assessment",
            "GitHub repository creation",
            "First data visualization project"
        ],
        skills: ["Python", "Pandas", "Matplotlib", "Git"],
        timeCommitment: "15 hours/week",
        successMetrics: "Complete 3 Python projects, GitHub portfolio setup",
        ariaSupport: "Daily coding challenges, project reviews, debugging help"
    },
    {
        id: 2,
        phase: "Foundation",
        month: 2,
        title: "Machine Learning Basics",
        date: "February 2025",
        status: "planned",
        description: "Wprowadzenie do uczenia maszynowego z focus na manufacturing",
        goals: [
            "Start Machine Learning Specialization (Andrew Ng)",
            "Quality Control ML project",
            "Trójmiasto AI Meetup attendance"
        ],
        milestones: [
            "First ML model deployment",
            "Manufacturing case study completion",
            "AI community networking"
        ],
        skills: ["Scikit-learn", "Linear Regression", "Classification", "Model Evaluation"],
        timeCommitment: "18 hours/week",
        successMetrics: "Deploy 2 ML models, present at local meetup",
        ariaSupport: "ML concept explanations, project guidance, presentation prep"
    },
    {
        id: 3,
        phase: "Skill Building",
        month: 3,
        title: "Advanced Analytics & Networking",
        date: "March 2025",
        status: "planned",
        description: "Pogłębienie umiejętności analitycznych i budowanie sieci kontaktów",
        goals: [
            "Global AI Bootcamp Kraków attendance",
            "PVD Process Optimization project",
            "LinkedIn networking strategy"
        ],
        milestones: [
            "Conference presentation or poster",
            "Advanced analytics project",
            "50+ LinkedIn connections in AI"
        ],
        skills: ["Advanced Statistics", "Feature Engineering", "Data Visualization", "Networking"],
        timeCommitment: "20 hours/week",
        successMetrics: "Conference participation, 3 completed projects",
        ariaSupport: "Conference prep, networking scripts, project optimization"
    },
    {
        id: 4,
        phase: "Skill Building",
        month: 4,
        title: "Deep Learning & Computer Vision",
        date: "April 2025",
        status: "planned",
        description: "Specjalizacja w computer vision dla aplikacji manufacturing",
        goals: [
            "Deep Learning Specialization start",
            "SMT Defect Detection CV project",
            "Data Science Summit Warsaw"
        ],
        milestones: [
            "CNN model for quality control",
            "Computer vision portfolio project",
            "Industry networking at summit"
        ],
        skills: ["TensorFlow", "CNN", "Computer Vision", "Image Processing"],
        timeCommitment: "22 hours/week",
        successMetrics: "Working CV model, summit presentation",
        ariaSupport: "Deep learning tutorials, CV project guidance, presentation coaching"
    },
    {
        id: 5,
        phase: "Specialization",
        month: 5,
        title: "AI in Manufacturing Focus",
        date: "May 2025",
        status: "planned",
        description: "Specjalizacja w AI dla przemysłu i Industry 4.0",
        goals: [
            "AI in Manufacturing Conference",
            "Predictive Maintenance project",
            "AWS ML Specialty prep start"
        ],
        milestones: [
            "Industry conference networking",
            "Predictive maintenance model",
            "AWS certification study plan"
        ],
        skills: ["Predictive Analytics", "IoT Integration", "AWS ML Services", "Industry 4.0"],
        timeCommitment: "25 hours/week",
        successMetrics: "Industry connections, working predictive model",
        ariaSupport: "Industry insights, AWS study plan, project architecture"
    },
    {
        id: 6,
        phase: "Specialization",
        month: 6,
        title: "Cloud ML & Certification Prep",
        date: "June 2025",
        status: "planned",
        description: "Przygotowanie do certyfikacji i cloud deployment",
        goals: [
            "AWS ML Specialty intensive prep",
            "Cloud ML pipeline project",
            "Portfolio website launch"
        ],
        milestones: [
            "AWS practice exams >80%",
            "Production ML pipeline",
            "Professional portfolio online"
        ],
        skills: ["AWS SageMaker", "MLOps", "Cloud Architecture", "Portfolio Development"],
        timeCommitment: "28 hours/week",
        successMetrics: "AWS exam readiness, live portfolio",
        ariaSupport: "Certification coaching, cloud tutorials, portfolio review"
    },
    {
        id: 7,
        phase: "Professional Development",
        month: 7,
        title: "Certification & Job Search Prep",
        date: "July 2025",
        status: "planned",
        description: "Zdobycie certyfikacji i przygotowanie do zmiany kariery",
        goals: [
            "AWS ML Specialty exam",
            "Job search strategy",
            "Interview preparation"
        ],
        milestones: [
            "AWS certification achieved",
            "Resume optimization",
            "Mock interviews completed"
        ],
        skills: ["Certification", "Interview Skills", "Resume Writing", "Job Search"],
        timeCommitment: "30 hours/week",
        successMetrics: "AWS certified, 10 job applications sent",
        ariaSupport: "Exam strategies, interview prep, resume optimization"
    },
    {
        id: 8,
        phase: "Professional Development",
        month: 8,
        title: "Advanced Projects & Applications",
        date: "August 2025",
        status: "planned",
        description: "Zaawansowane projekty i aktywne poszukiwanie pracy",
        goals: [
            "Capstone project: End-to-end ML solution",
            "Active job applications",
            "Technical interviews"
        ],
        milestones: [
            "Complex ML system deployment",
            "First round interviews",
            "Salary negotiations prep"
        ],
        skills: ["System Design", "Technical Interviews", "Negotiation", "Project Management"],
        timeCommitment: "35 hours/week",
        successMetrics: "Capstone completed, 5 interviews scheduled",
        ariaSupport: "Project mentoring, interview coaching, negotiation advice"
    },
    {
        id: 9,
        phase: "Transition",
        month: 9,
        title: "Job Offers & Transition Planning",
        date: "September 2025",
        status: "planned",
        description: "Finalizacja ofert pracy i planowanie przejścia",
        goals: [
            "Job offer evaluation",
            "Transition timeline planning",
            "Knowledge transfer at current role"
        ],
        milestones: [
            "Job offer received",
            "Transition plan created",
            "Current role handover"
        ],
        skills: ["Decision Making", "Transition Planning", "Knowledge Transfer", "Professional Ethics"],
        timeCommitment: "Variable",
        successMetrics: "Job offer accepted, smooth transition planned",
        ariaSupport: "Offer evaluation, transition planning, career advice"
    },
    {
        id: 10,
        phase: "Transition",
        month: 10,
        title: "New Role Onboarding",
        date: "October 2025",
        status: "planned",
        description: "Rozpoczęcie nowej roli w AI/Data Science",
        goals: [
            "Successful onboarding",
            "First 90 days plan",
            "Team integration"
        ],
        milestones: [
            "Role transition completed",
            "First project assignment",
            "Team relationships established"
        ],
        skills: ["Onboarding", "Team Collaboration", "New Technology Adoption", "Performance"],
        timeCommitment: "Full-time + learning",
        successMetrics: "Successful first month, positive feedback",
        ariaSupport: "Onboarding guidance, performance tips, continuous learning"
    },
    {
        id: 11,
        phase: "Growth",
        month: 11,
        title: "Performance & Advanced Learning",
        date: "November 2025",
        status: "planned",
        description: "Wysokie performance w nowej roli i dalszy rozwój",
        goals: [
            "Exceed performance expectations",
            "Advanced specialization choice",
            "Mentoring others"
        ],
        milestones: [
            "Positive performance review",
            "Advanced certification planning",
            "First mentoring relationship"
        ],
        skills: ["Advanced ML", "Leadership", "Mentoring", "Strategic Thinking"],
        timeCommitment: "Full-time + 10 hours learning",
        successMetrics: "Top performer rating, mentoring assignment",
        ariaSupport: "Performance optimization, advanced learning paths, leadership development"
    },
    {
        id: 12,
        phase: "Growth",
        month: 12,
        title: "Leadership & Future Planning",
        date: "December 2025",
        status: "planned",
        description: "Przywództwo w zespole i planowanie dalszej kariery",
        goals: [
            "Leadership responsibilities",
            "2026 career planning",
            "Industry recognition"
        ],
        milestones: [
            "Team lead or senior role",
            "Industry conference speaking",
            "Salary increase achieved"
        ],
        skills: ["Team Leadership", "Strategic Planning", "Public Speaking", "Industry Expertise"],
        timeCommitment: "Full-time + strategic activities",
        successMetrics: "Leadership role, €120k+ salary, industry recognition",
        ariaSupport: "Leadership coaching, strategic planning, personal branding"
    }
];

// Voice interface functionality
let isVoiceActive = false;
let recognition = null;
let synthesis = null;

// Initialize sections with full data
function initializeAllSections() {
    populateCoursesSection();
    populateCertificationsSection();
    populatePortfolioSection();
    populateNetworkingSection();
    populateRoadmapSection();
    initializeVoiceInterface();
    console.log('🚀 All sections initialized with full data');
}

// Populate networking events section
function populateNetworkingSection() {
    const networkingGrid = document.getElementById('networkingGrid');
    if (!networkingGrid) return;

    networkingGrid.innerHTML = polishAIEventsData.map(event => `
        <div class="networking-card ${event.status}" data-event-id="${event.id}">
            <div class="event-status-badge ${event.status}">
                ${getEventStatusLabel(event.status)}
            </div>

            <div class="event-header">
                <div class="event-date">
                    <div class="date-day">${new Date(event.date).getDate()}</div>
                    <div class="date-month">${new Date(event.date).toLocaleDateString('pl-PL', { month: 'short' })}</div>
                    <div class="date-year">${new Date(event.date).getFullYear()}</div>
                </div>
                <div class="event-info">
                    <h3>${event.name}</h3>
                    <div class="event-meta">
                        <span class="location"><i class="fas fa-map-marker-alt"></i> ${event.location}</span>
                        <span class="format"><i class="fas fa-${event.format === 'Hybrid' ? 'globe' : event.format === 'Online' ? 'laptop' : 'users'}"></i> ${event.format}</span>
                        <span class="attendees"><i class="fas fa-users"></i> ${event.attendees} uczestników</span>
                    </div>
                </div>
                <div class="event-cost ${event.cost === 'Free' ? 'free' : 'paid'}">
                    ${event.cost}
                </div>
            </div>

            <div class="event-description">
                <p>${event.description}</p>
            </div>

            <div class="event-relevance">
                <span class="relevance-label">Relevance for Andrii:</span>
                <div class="relevance-bar">
                    <div class="relevance-fill" style="width: ${event.relevance}%"></div>
                </div>
                <span class="relevance-value">${event.relevance}%</span>
            </div>

            <div class="event-topics">
                <h4>Key Topics:</h4>
                <div class="topics-list">
                    ${event.keyTopics.map(topic => `<span class="topic-tag">${topic}</span>`).join('')}
                </div>
            </div>

            <div class="manufacturing-focus">
                <h4><i class="fas fa-industry"></i> Manufacturing Focus</h4>
                <p>${event.manufacturingFocus}</p>
            </div>

            <div class="networking-value">
                <h4><i class="fas fa-handshake"></i> Networking Value</h4>
                <p>${event.networkingValue}</p>
            </div>

            <div class="event-speakers">
                <h4>Featured Speakers:</h4>
                <ul class="speakers-list">
                    ${event.speakers.map(speaker => `<li>${speaker}</li>`).join('')}
                </ul>
            </div>

            <div class="event-details">
                <div class="detail-item">
                    <span class="detail-label">Registration Deadline:</span>
                    <span class="detail-value">${new Date(event.registrationDeadline).toLocaleDateString('pl-PL')}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Organizer:</span>
                    <span class="detail-value">${event.organizer}</span>
                </div>
            </div>

            <div class="event-actions">
                <button class="event-btn primary" onclick="registerForEvent(${event.id})">
                    <i class="fas fa-user-plus"></i>
                    Register Now
                </button>
                <button class="event-btn secondary" onclick="addToCalendar(${event.id})">
                    <i class="fas fa-calendar-plus"></i>
                    Add to Calendar
                </button>
                <button class="event-btn tertiary" onclick="prepareForEvent(${event.id})">
                    <i class="fas fa-preparation"></i>
                    Prepare
                </button>
            </div>
        </div>
    `).join('');
}

// Populate career roadmap section
function populateRoadmapSection() {
    const roadmapTimeline = document.getElementById('roadmapTimeline');
    if (!roadmapTimeline) return;

    roadmapTimeline.innerHTML = `
        <div class="roadmap-header">
            <h2>12-Month Career Transformation Plan</h2>
            <p>From Manufacturing Engineering to AI/Data Science Leadership</p>
            <div class="roadmap-progress">
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 8.33%"></div>
                </div>
                <span class="progress-text">Month 1 of 12 (8% Complete)</span>
            </div>
        </div>

        <div class="roadmap-phases">
            ${getUniquePhases().map(phase => `
                <div class="phase-section">
                    <h3 class="phase-title">${phase}</h3>
                    <div class="phase-timeline">
                        ${careerRoadmapData.filter(item => item.phase === phase).map(milestone => `
                            <div class="roadmap-milestone ${milestone.status}" data-milestone-id="${milestone.id}">
                                <div class="milestone-marker">
                                    <div class="milestone-number">${milestone.month}</div>
                                    <div class="milestone-connector"></div>
                                </div>

                                <div class="milestone-content">
                                    <div class="milestone-header">
                                        <h4>${milestone.title}</h4>
                                        <span class="milestone-date">${milestone.date}</span>
                                        <span class="milestone-status ${milestone.status}">${getMilestoneStatusLabel(milestone.status)}</span>
                                    </div>

                                    <p class="milestone-description">${milestone.description}</p>

                                    <div class="milestone-goals">
                                        <h5><i class="fas fa-target"></i> Goals:</h5>
                                        <ul>
                                            ${milestone.goals.map(goal => `<li>${goal}</li>`).join('')}
                                        </ul>
                                    </div>

                                    <div class="milestone-skills">
                                        <h5><i class="fas fa-brain"></i> Skills to Develop:</h5>
                                        <div class="skills-tags">
                                            ${milestone.skills.map(skill => `<span class="skill-tag">${skill}</span>`).join('')}
                                        </div>
                                    </div>

                                    <div class="milestone-metrics">
                                        <div class="metric-item">
                                            <span class="metric-label">Time Commitment:</span>
                                            <span class="metric-value">${milestone.timeCommitment}</span>
                                        </div>
                                        <div class="metric-item">
                                            <span class="metric-label">Success Metrics:</span>
                                            <span class="metric-value">${milestone.successMetrics}</span>
                                        </div>
                                    </div>

                                    <div class="aria-support">
                                        <h5><i class="fas fa-robot"></i> ARIA Support:</h5>
                                        <p>${milestone.ariaSupport}</p>
                                    </div>

                                    <div class="milestone-actions">
                                        <button class="milestone-btn primary" onclick="startMilestone(${milestone.id})">
                                            <i class="fas fa-play"></i>
                                            ${milestone.status === 'current' ? 'Continue' : 'Start Planning'}
                                        </button>
                                        <button class="milestone-btn secondary" onclick="viewMilestoneDetails(${milestone.id})">
                                            <i class="fas fa-info-circle"></i>
                                            Details
                                        </button>
                                        <button class="milestone-btn tertiary" onclick="adjustMilestone(${milestone.id})">
                                            <i class="fas fa-edit"></i>
                                            Customize
                                        </button>
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `).join('')}
        </div>

        <div class="roadmap-summary">
            <h3>Transformation Summary</h3>
            <div class="summary-grid">
                <div class="summary-item">
                    <div class="summary-icon"><i class="fas fa-graduation-cap"></i></div>
                    <div class="summary-content">
                        <h4>Education</h4>
                        <p>5 major certifications, 15+ courses completed</p>
                    </div>
                </div>
                <div class="summary-item">
                    <div class="summary-icon"><i class="fas fa-project-diagram"></i></div>
                    <div class="summary-content">
                        <h4>Portfolio</h4>
                        <p>12+ projects showcasing manufacturing + AI expertise</p>
                    </div>
                </div>
                <div class="summary-item">
                    <div class="summary-icon"><i class="fas fa-users"></i></div>
                    <div class="summary-content">
                        <h4>Network</h4>
                        <p>200+ AI professionals, 6 industry events attended</p>
                    </div>
                </div>
                <div class="summary-item">
                    <div class="summary-icon"><i class="fas fa-chart-line"></i></div>
                    <div class="summary-content">
                        <h4>Career Growth</h4>
                        <p>€120k+ salary, Senior Data Scientist role</p>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Initialize voice interface
function initializeVoiceInterface() {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        recognition = new SpeechRecognition();

        recognition.continuous = false;
        recognition.interimResults = false;
        recognition.lang = 'pl-PL';

        recognition.onstart = function() {
            isVoiceActive = true;
            updateVoiceStatus('Słucham... Mów teraz!');
            document.getElementById('startVoice').disabled = true;
            document.getElementById('stopVoice').disabled = false;
        };

        recognition.onresult = function(event) {
            const transcript = event.results[0][0].transcript;
            document.getElementById('voiceTranscript').textContent = transcript;

            // Send transcript to ARIA
            addUserMessage(transcript);
            setTimeout(() => {
                processARIAResponse(transcript);
            }, 1000);

            updateVoiceStatus('Rozpoznano: "' + transcript + '"');
        };

        recognition.onerror = function(event) {
            updateVoiceStatus('Błąd rozpoznawania: ' + event.error);
            resetVoiceControls();
        };

        recognition.onend = function() {
            isVoiceActive = false;
            updateVoiceStatus('Rozpoznawanie zakończone.');
            resetVoiceControls();
        };

        // Initialize speech synthesis
        if ('speechSynthesis' in window) {
            synthesis = window.speechSynthesis;
        }

        console.log('🎤 Voice interface initialized');
    } else {
        console.log('❌ Speech recognition not supported');
    }
}

// Helper functions for roadmap
function getUniquePhases() {
    return [...new Set(careerRoadmapData.map(item => item.phase))];
}

function getMilestoneStatusLabel(status) {
    const labels = {
        'current': 'W trakcie',
        'planned': 'Zaplanowane',
        'completed': 'Ukończone',
        'delayed': 'Opóźnione'
    };
    return labels[status] || status;
}

function getEventStatusLabel(status) {
    const labels = {
        'recommended': 'Polecane',
        'priority': 'Priorytet',
        'planned': 'Zaplanowane',
        'must-attend': 'Obowiązkowe',
        'considering': 'Rozważane',
        'supporting': 'Wspierające'
    };
    return labels[status] || status;
}

// Update the main initialization
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 AI Coach ARIA initializing...');

    initializeParticles();
    setupNavigation();
    initializeARIA();
    loadUserProgress();

    // Initialize all sections after a short delay
    setTimeout(() => {
        initializeDashboard();
        initializeAllSections();
    }, 500);

    console.log('✅ ARIA platform ready with all sections!');
});

// Step 4: Button handlers for networking events and roadmap milestones
// Networking event handlers
function registerForEvent(eventId) {
    const event = polishAIEventsData.find(e => e.id === eventId);
    if (!event) return;

    console.log(`📅 Registering for event: ${event.name}`);

    event.status = 'registered';
    populateNetworkingSection();

    addARIAMessage(`Świetnie! Zarejestrowałem Cię na "${event.name}"!

📅 **Szczegóły wydarzenia:**
• Data: ${new Date(event.date).toLocaleDateString('pl-PL', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}
• Lokalizacja: ${event.location}
• Format: ${event.format}
• Koszt: ${event.cost}
• Uczestnicy: ${event.attendees} osób

🎯 **Relevance dla Ciebie: ${event.relevance}%**

**Manufacturing Focus:**
${event.manufacturingFocus}

**Networking Value:**
${event.networkingValue}

**Featured Speakers:**
${event.speakers.map(speaker => `• ${speaker}`).join('\n')}

**Deadline rejestracji:** ${new Date(event.registrationDeadline).toLocaleDateString('pl-PL')}

Czy chcesz, żebym pomógł Ci przygotować się do tego wydarzenia?`);

    showQuickReplies([
        "Przygotuj elevator pitch",
        "Zaplanuj networking",
        "Dodaj do kalendarza",
        "Inne wydarzenia"
    ]);

    showNotification(`Zarejestrowano na: ${event.name}`, 'success');

    // Add achievement
    const achievement = {
        id: Date.now(),
        title: `Registered for ${event.name}`,
        description: `Networking opportunity in AI community`,
        date: new Date().toISOString(),
        impact: "+5% Networking Skills",
        icon: "fas fa-calendar-check"
    };

    userProgress.achievements.push(achievement);
    saveUserProgress();
    populateAchievements();
}

function addToCalendar(eventId) {
    const event = polishAIEventsData.find(e => e.id === eventId);
    if (!event) return;

    console.log(`📅 Adding to calendar: ${event.name}`);

    const startDate = new Date(event.date);
    const endDate = new Date(startDate.getTime() + 8 * 60 * 60 * 1000); // 8 hours duration

    const calendarEvent = {
        title: event.name,
        start: startDate.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z',
        end: endDate.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z',
        description: `${event.description}\\n\\nLocation: ${event.location}\\nWebsite: ${event.website}`,
        location: event.location
    };

    const googleCalendarUrl = `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${encodeURIComponent(calendarEvent.title)}&dates=${calendarEvent.start}/${calendarEvent.end}&details=${encodeURIComponent(calendarEvent.description)}&location=${encodeURIComponent(calendarEvent.location)}`;

    addARIAMessage(`Dodaję "${event.name}" do Twojego kalendarza!

📅 **Szczegóły kalendarza:**
• Tytuł: ${event.name}
• Data: ${startDate.toLocaleDateString('pl-PL')} ${startDate.toLocaleTimeString('pl-PL', { hour: '2-digit', minute: '2-digit' })}
• Lokalizacja: ${event.location}
• Czas trwania: ~8 godzin

**Przypomnienia, które ustawię:**
• 1 tydzień przed - przygotowanie materiałów
• 3 dni przed - przegląd agendy i speakers
• 1 dzień przed - przygotowanie elevator pitch
• 2 godziny przed - dojazd i networking prep

Kliknij link poniżej, aby dodać do Google Calendar:
${googleCalendarUrl}

Czy chcesz, żebym ustawił dodatkowe przypomnienia?`);

    showQuickReplies([
        "Ustaw przypomnienia",
        "Przygotuj agenda",
        "Networking strategy",
        "Inne wydarzenia"
    ]);

    showNotification('Link do kalendarza w czacie ARIA', 'info');
}

function prepareForEvent(eventId) {
    const event = polishAIEventsData.find(e => e.id === eventId);
    if (!event) return;

    console.log(`🎯 Preparing for event: ${event.name}`);

    const preparationPlan = {
        "1 week before": [
            "Research speakers and their backgrounds",
            "Review event agenda and key topics",
            "Prepare questions for Q&A sessions",
            "Update LinkedIn profile with current projects"
        ],
        "3 days before": [
            "Prepare elevator pitch (30-second version)",
            "Print business cards or prepare digital contact sharing",
            "Research attendee list if available",
            "Plan travel and accommodation"
        ],
        "1 day before": [
            "Review manufacturing + AI talking points",
            "Prepare portfolio examples on mobile device",
            "Check event updates and last-minute changes",
            "Set networking goals (5-10 meaningful connections)"
        ],
        "Day of event": [
            "Arrive early for better networking opportunities",
            "Attend welcome session and networking breaks",
            "Take notes during presentations",
            "Follow up with new connections within 24 hours"
        ]
    };

    addARIAMessage(`Przygotowuję plan dla "${event.name}"!

🎯 **Cele networking dla tego wydarzenia:**
• Poznanie 5-10 ekspertów AI w manufacturing
• Zdobycie kontaktów do potencjalnych mentorów
• Prezentacja Twojego unique value proposition (Manufacturing + AI)
• Uczenie się o najnowszych trendach w branży

📋 **Plan przygotowań:**

**1 tydzień przed:**
${preparationPlan["1 week before"].map(item => `• ${item}`).join('\n')}

**3 dni przed:**
${preparationPlan["3 days before"].map(item => `• ${item}`).join('\n')}

**1 dzień przed:**
${preparationPlan["1 day before"].map(item => `• ${item}`).join('\n')}

**W dniu wydarzenia:**
${preparationPlan["Day of event"].map(item => `• ${item}`).join('\n')}

🎤 **Twój elevator pitch (draft):**
"Cześć, jestem Andrii, Process Engineer w Aptiv z 15-letnim doświadczeniem w manufacturing. Obecnie transformuję swoją karierę w kierunku AI/Data Science, łącząc expertise w SMT/CBA z machine learning. Pracuję nad projektami computer vision dla quality control i predictive maintenance. Szukam możliwości współpracy i mentoringu w AI for manufacturing."

Czy chcesz, żebym pomógł Ci dopracować elevator pitch?`);

    showQuickReplies([
        "Dopracuj elevator pitch",
        "Networking strategy",
        "Przygotuj pytania",
        "Portfolio prep"
    ]);

    showNotification(`Plan przygotowań dla ${event.name} gotowy!`, 'success');
}

// Career roadmap milestone handlers
function startMilestone(milestoneId) {
    const milestone = careerRoadmapData.find(m => m.id === milestoneId);
    if (!milestone) return;

    console.log(`🚀 Starting milestone: ${milestone.title}`);

    milestone.status = 'current';
    populateRoadmapSection();

    addARIAMessage(`Rozpoczynamy milestone: "${milestone.title}"!

📅 **Timeline:** ${milestone.date} (Miesiąc ${milestone.month})
🎯 **Faza:** ${milestone.phase}
⏰ **Commitment:** ${milestone.timeCommitment}

**Opis:**
${milestone.description}

**Główne cele:**
${milestone.goals.map(goal => `• ${goal}`).join('\n')}

**Umiejętności do rozwoju:**
${milestone.skills.map(skill => `• ${skill}`).join('\n')}

**Metryki sukcesu:**
${milestone.successMetrics}

**Jak ARIA Ci pomoże:**
${milestone.ariaSupport}

**Plan działania na najbliższe 2 tygodnie:**
1. Tydzień 1: Setup i podstawy
2. Tydzień 2: Pierwszy projekt praktyczny
3. Tydzień 3: Pogłębienie wiedzy
4. Tydzień 4: Finalizacja i przegląd

Czy chcesz, żebym stworzył szczegółowy harmonogram tygodniowy?`);

    showQuickReplies([
        "Stwórz harmonogram",
        "Pokaż materiały",
        "Ustaw przypomnienia",
        "Dostosuj plan"
    ]);

    showNotification(`Rozpoczęto: ${milestone.title}`, 'success');

    // Add achievement
    const achievement = {
        id: Date.now(),
        title: `Started ${milestone.title}`,
        description: `Beginning ${milestone.phase} phase of career transformation`,
        date: new Date().toISOString(),
        impact: `+${Math.floor(Math.random() * 5 + 3)}% Career Progress`,
        icon: "fas fa-rocket"
    };

    userProgress.achievements.push(achievement);
    userProgress.careerProgress = Math.min(userProgress.careerProgress + 3, 100);
    saveUserProgress();
    populateAchievements();
    createProgressGauge();
}

function viewMilestoneDetails(milestoneId) {
    const milestone = careerRoadmapData.find(m => m.id === milestoneId);
    if (!milestone) return;

    console.log(`📖 Viewing milestone details: ${milestone.title}`);

    addARIAMessage(`Szczegółowe informacje o milestone "${milestone.title}":

📊 **Podstawowe informacje:**
• Faza: ${milestone.phase}
• Miesiąc: ${milestone.month}/12
• Status: ${getMilestoneStatusLabel(milestone.status)}
• Commitment: ${milestone.timeCommitment}

📝 **Szczegółowy opis:**
${milestone.description}

🎯 **Cele do osiągnięcia:**
${milestone.goals.map((goal, index) => `${index + 1}. ${goal}`).join('\n')}

🧠 **Umiejętności techniczne:**
${milestone.skills.map(skill => `• ${skill}`).join('\n')}

📈 **Kluczowe milestones:**
${milestone.milestones.map((ms, index) => `${index + 1}. ${ms}`).join('\n')}

✅ **Metryki sukcesu:**
${milestone.successMetrics}

🤖 **Wsparcie ARIA:**
${milestone.ariaSupport}

**Powiązane zasoby:**
• Kursy: Machine Learning Specialization, Python for Data Analysis
• Projekty: SMT Quality Control, Manufacturing Analytics
• Wydarzenia: ${milestone.month <= 3 ? 'Trójmiasto AI Meetup, Global AI Bootcamp' : milestone.month <= 6 ? 'Data Science Summit, AI Manufacturing Conference' : 'Industry networking, Job interviews'}
• Certyfikacje: ${milestone.month >= 6 ? 'AWS ML Specialty preparation' : 'Foundation building'}

Czy chcesz rozpocząć ten milestone lub dostosować plan?`);

    showQuickReplies([
        "Rozpocznij milestone",
        "Dostosuj plan",
        "Pokaż zasoby",
        "Następny milestone"
    ]);

    showNotification('Szczegóły milestone w czacie ARIA', 'info');
}

function adjustMilestone(milestoneId) {
    const milestone = careerRoadmapData.find(m => m.id === milestoneId);
    if (!milestone) return;

    console.log(`⚙️ Adjusting milestone: ${milestone.title}`);

    addARIAMessage(`Dostosowuję milestone "${milestone.title}" do Twoich potrzeb!

🎛️ **Opcje dostosowania:**

**1. Time Commitment:**
• Obecny: ${milestone.timeCommitment}
• Opcje: 10h/week (light), 15h/week (standard), 20h/week (intensive), 25h+ (accelerated)

**2. Focus Areas:**
• Manufacturing Applications (więcej projektów przemysłowych)
• Technical Depth (głębsze umiejętności programistyczne)
• Business Skills (leadership i management)
• Networking (więcej wydarzeń i kontaktów)

**3. Timeline:**
• Przyspiesz (ukończ w 3 tygodnie)
• Standard (4 tygodnie jak planowane)
• Rozszerz (6 tygodni z dodatkowymi projektami)

**4. Learning Style:**
• Project-based (więcej hands-on projektów)
• Theory-first (więcej teorii przed praktyką)
• Balanced (mix teorii i praktyki)

**5. Support Level:**
• Minimal (samodzielna nauka)
• Standard (cotygodniowe check-iny z ARIA)
• Intensive (daily guidance i feedback)

**Moje rekomendacje dla Ciebie:**
• Time: 18-20h/week (optimal dla working professional)
• Focus: Manufacturing Applications + Technical Depth
• Timeline: Standard z możliwością przyspieszenia
• Style: Project-based (wykorzystuje Twoje doświadczenie)
• Support: Standard z intensive w kluczowych momentach

Które aspekty chcesz dostosować?`);

    showQuickReplies([
        "Dostosuj czas",
        "Zmień focus",
        "Przyspiesz timeline",
        "Więcej wsparcia"
    ]);

    showNotification(`Opcje dostosowania dla ${milestone.title}`, 'info');
}

// Voice interface handlers
function updateVoiceStatus(status) {
    const voiceStatus = document.getElementById('voiceStatus');
    if (voiceStatus) {
        voiceStatus.textContent = status;
    }
}

function resetVoiceControls() {
    const startBtn = document.getElementById('startVoice');
    const stopBtn = document.getElementById('stopVoice');

    if (startBtn) startBtn.disabled = false;
    if (stopBtn) stopBtn.disabled = true;
}

function startVoiceRecognition() {
    if (recognition && !isVoiceActive) {
        recognition.start();
    }
}

function stopVoiceRecognition() {
    if (recognition && isVoiceActive) {
        recognition.stop();
    }
}

// Setup voice controls
document.addEventListener('DOMContentLoaded', function() {
    const startVoiceBtn = document.getElementById('startVoice');
    const stopVoiceBtn = document.getElementById('stopVoice');

    if (startVoiceBtn) {
        startVoiceBtn.addEventListener('click', startVoiceRecognition);
    }

    if (stopVoiceBtn) {
        stopVoiceBtn.addEventListener('click', stopVoiceRecognition);
    }
});

// Export new functions
window.registerForEvent = registerForEvent;
window.addToCalendar = addToCalendar;
window.prepareForEvent = prepareForEvent;
window.startMilestone = startMilestone;
window.viewMilestoneDetails = viewMilestoneDetails;
window.adjustMilestone = adjustMilestone;
window.startVoiceRecognition = startVoiceRecognition;
window.stopVoiceRecognition = stopVoiceRecognition;

// Step 5: Advanced Features and Finalization
// Advanced ARIA conversation processing
function processARIAResponse(userInput) {
    const input = userInput.toLowerCase();
    let response = "";

    // Advanced conversation logic based on context and keywords
    if (input.includes('kurs') || input.includes('nauka') || input.includes('uczenie')) {
        response = handleCourseInquiry(input);
    } else if (input.includes('certyfikacja') || input.includes('egzamin') || input.includes('aws') || input.includes('azure')) {
        response = handleCertificationInquiry(input);
    } else if (input.includes('portfolio') || input.includes('projekt') || input.includes('github')) {
        response = handlePortfolioInquiry(input);
    } else if (input.includes('networking') || input.includes('wydarzenie') || input.includes('konferencja')) {
        response = handleNetworkingInquiry(input);
    } else if (input.includes('praca') || input.includes('kariera') || input.includes('cv') || input.includes('wywiad')) {
        response = handleCareerInquiry(input);
    } else if (input.includes('manufacturing') || input.includes('przemysł') || input.includes('smt') || input.includes('pvd')) {
        response = handleManufacturingInquiry(input);
    } else if (input.includes('ai') || input.includes('machine learning') || input.includes('data science')) {
        response = handleAIInquiry(input);
    } else if (input.includes('pomoc') || input.includes('help') || input.includes('jak')) {
        response = handleHelpInquiry(input);
    } else {
        response = handleGeneralInquiry(input);
    }

    addARIAMessage(response);

    // Generate contextual quick replies
    generateContextualQuickReplies(input);
}

function handleCourseInquiry(input) {
    const responses = [
        `Na podstawie Twojego profilu w manufacturing, polecam zacząć od Machine Learning Specialization Andrew Ng. Ma 98% relevance dla Twojej transformacji! Kurs łączy teorię z praktycznymi aplikacjami w przemyśle.`,

        `Widzę, że interesują Cię kursy! Dla Twojego background w SMT/CBA idealny będzie Google Data Analytics Certificate. Nauczysz się analizować dane produkcyjne i tworzyć KPI dashboards.`,

        `Świetne pytanie o kursy! Z Twoim doświadczeniem w PVD technology, Digital Manufacturing & Design Technology będzie perfect fit. Skupia się na Industry 4.0 i AI w przemyśle.`
    ];

    return responses[Math.floor(Math.random() * responses.length)];
}

function handleCertificationInquiry(input) {
    if (input.includes('aws')) {
        return `AWS ML Specialty to najlepsza inwestycja dla Ciebie! ROI 4.0x, koszt $300, potencjalny wzrost wynagrodzeń €15k-25k. Perfect dla manufacturing professionals przechodzących do AI. Czy chcesz, żebym stworzył plan przygotowań?`;
    } else if (input.includes('azure')) {
        return `Azure AI Engineer Associate to świetny wybór! ROI 3.5x, koszt $165. Microsoft ma strong presence w enterprise manufacturing. Certyfikacja idealnie pasuje do corporate environments jak Aptiv.`;
    } else {
        return `Certyfikacje to klucz do transformacji kariery! Na podstawie Twojego profilu ranking to: 1) AWS ML Specialty (ROI 4.0x), 2) Azure AI Engineer (ROI 3.5x), 3) Udacity AI for Manufacturing (ROI 2.8x). Która Cię najbardziej interesuje?`;
    }
}

function handlePortfolioInquiry(input) {
    const manufacturingProjects = [
        "SMT Quality Control Computer Vision - automated defect detection using OpenCV and TensorFlow",
        "PVD Process Optimization ML - predictive model for coating quality using sensor data",
        "Manufacturing Data Pipeline - real-time analytics dashboard for production KPIs",
        "Predictive Maintenance System - ML model predicting equipment failures"
    ];

    return `Portfolio to Twoja wizytówka! Z doświadczeniem w manufacturing masz unique advantage. Polecam zacząć od GitHub Pages (free) i stworzyć te projekty:

${manufacturingProjects.map((project, index) => `${index + 1}. ${project}`).join('\n')}

Każdy projekt pokazuje Twoje domain expertise + AI skills. Który chcesz zacząć pierwszy?`;
}

function handleNetworkingInquiry(input) {
    return `Networking to accelerator kariery! Dla Ciebie w Gdańsku perfect start to Trójmiasto AI Meetup (25 stycznia). Blisko, free, 94% relevance. Potem Global AI Bootcamp Kraków (7 marca) - największe AI event w Polsce!

🎯 Twój networking goal: poznać 5-10 AI professionals w manufacturing. Masz unique value proposition: 15 lat manufacturing + AI transformation journey.

Czy chcesz, żebym przygotował Twój elevator pitch?`;
}

function handleCareerInquiry(input) {
    return `Twoja transformacja kariery z Manufacturing Engineer do AI/Data Scientist to realistic i achievable! Timeline: 12 miesięcy do Senior Data Scientist role z €120k+ salary.

🚀 **Twoje competitive advantages:**
• 15 lat manufacturing experience (rare w AI community)
• SMT/CBA expertise (high demand w automotive AI)
• PVD technology knowledge (unique w coating industry)
• Process engineering mindset (perfect dla ML pipelines)

**Next steps:** Start z Python fundamentals, potem ML specialization. Czy chcesz szczegółowy plan na pierwsze 3 miesiące?`;
}

function handleManufacturingInquiry(input) {
    return `Manufacturing + AI to explosive combination! Twoje doświadczenie w SMT/CBA i PVD to goldmine dla AI applications:

🏭 **AI opportunities w manufacturing:**
• Computer Vision dla quality control (Twoja SMT expertise!)
• Predictive maintenance (sensor data analysis)
• Process optimization (ML dla PVD parameters)
• Digital twins (virtual manufacturing models)

**Market demand:** Manufacturing AI engineers earn €100k-150k w Polsce, €120k-200k w EU. Shortage of professionals z Twoim background!

Który manufacturing process chcesz first optimize z AI?`;
}

function handleAIInquiry(input) {
    return `AI/Machine Learning to future of manufacturing! Z Twoim background masz perfect foundation:

🧠 **AI skills roadmap dla Ciebie:**
1. Python + Data Analysis (Month 1-2)
2. Machine Learning fundamentals (Month 3-4)
3. Computer Vision dla quality control (Month 5-6)
4. MLOps + Cloud deployment (Month 7-8)

**Manufacturing AI applications:**
• Defect detection w SMT assembly
• Predictive quality w PVD coating
• Process parameter optimization
• Real-time production analytics

Która AI technology najbardziej Cię fascynuje?`;
}

function handleHelpInquiry(input) {
    return `Jestem tutaj, żeby pomóc w Twojej transformacji kariery! 🤖

**Co mogę dla Ciebie zrobić:**
• Planowanie learning path (kursy, certyfikacje)
• Networking strategy (wydarzenia, contacts)
• Portfolio development (projekty, GitHub)
• Career coaching (CV, interviews, salary negotiation)
• Technical guidance (Python, ML, AI)

**Popularne pytania:**
• "Jak zacząć z machine learning?"
• "Które certyfikacje mają najlepszy ROI?"
• "Jak wykorzystać manufacturing experience w AI?"
• "Gdzie znaleźć AI job w Polsce?"

O czym chcesz porozmawiać?`;
}

function handleGeneralInquiry(input) {
    const responses = [
        `Interesting question! Z Twoim background w manufacturing i celami w AI, mogę pomóc Ci znaleźć best path forward. Czy chcesz porozmawiać o konkretnym aspekcie Twojej transformacji kariery?`,

        `Great point! Jako AI coach specjalizujący się w manufacturing professionals, widzę huge potential w Twojej journey. Co najbardziej Cię teraz interesuje - learning, networking, czy career planning?`,

        `Rozumiem Twoje pytanie! Z 15-letnim doświadczeniem w manufacturing masz unique advantage w AI transformation. Czy chcesz, żebym pomógł Ci zaplanować następne kroki?`
    ];

    return responses[Math.floor(Math.random() * responses.length)];
}

function generateContextualQuickReplies(input) {
    let replies = [];

    if (input.includes('kurs') || input.includes('nauka')) {
        replies = ["Pokaż kursy", "Plan nauki", "Certyfikacje", "Projekty"];
    } else if (input.includes('certyfikacja')) {
        replies = ["AWS ML Specialty", "Azure AI Engineer", "Porównaj ROI", "Plan przygotowań"];
    } else if (input.includes('portfolio')) {
        replies = ["GitHub setup", "Pierwszy projekt", "Manufacturing AI", "CV optimization"];
    } else if (input.includes('networking')) {
        replies = ["Najbliższe wydarzenia", "Elevator pitch", "LinkedIn strategy", "Mentoring"];
    } else if (input.includes('praca') || input.includes('kariera')) {
        replies = ["Job search strategy", "Salary negotiation", "Interview prep", "CV review"];
    } else {
        replies = ["Zaplanuj naukę", "Znajdź wydarzenia", "Stwórz portfolio", "Career roadmap"];
    }

    showQuickReplies(replies);
}

// Advanced progress tracking and analytics
function updateDetailedProgress() {
    const progressData = {
        overall: userProgress.careerProgress,
        courses: calculateCoursesProgress(),
        certifications: calculateCertificationsProgress(),
        portfolio: calculatePortfolioProgress(),
        networking: calculateNetworkingProgress(),
        skills: calculateSkillsProgress()
    };

    // Update progress visualizations
    updateProgressCharts(progressData);

    // Generate insights
    generateProgressInsights(progressData);

    return progressData;
}

function calculateCoursesProgress() {
    const totalCourses = courseraCoursesData.length;
    const completedCourses = courseraCoursesData.filter(c => c.status === 'completed').length;
    const inProgressCourses = courseraCoursesData.filter(c => c.status === 'in-progress').length;

    return {
        total: totalCourses,
        completed: completedCourses,
        inProgress: inProgressCourses,
        percentage: Math.round((completedCourses + inProgressCourses * 0.5) / totalCourses * 100)
    };
}

function calculateCertificationsProgress() {
    const totalCerts = premiumCertificationsData.length;
    const completedCerts = premiumCertificationsData.filter(c => c.status === 'completed').length;
    const inProgressCerts = premiumCertificationsData.filter(c => c.status === 'in-progress').length;

    return {
        total: totalCerts,
        completed: completedCerts,
        inProgress: inProgressCerts,
        percentage: Math.round((completedCerts + inProgressCerts * 0.3) / totalCerts * 100)
    };
}

function calculatePortfolioProgress() {
    const totalPlatforms = portfolioPlatformsData.length;
    const activePlatforms = portfolioPlatformsData.filter(p => p.status === 'in-progress' || p.status === 'completed').length;

    return {
        total: totalPlatforms,
        active: activePlatforms,
        percentage: Math.round(activePlatforms / totalPlatforms * 100)
    };
}

function calculateNetworkingProgress() {
    const totalEvents = polishAIEventsData.length;
    const registeredEvents = polishAIEventsData.filter(e => e.status === 'registered' || e.status === 'attended').length;

    return {
        total: totalEvents,
        registered: registeredEvents,
        percentage: Math.round(registeredEvents / totalEvents * 100)
    };
}

function calculateSkillsProgress() {
    // Based on completed courses and projects
    const skillsData = {
        python: Math.min(userProgress.careerProgress * 2, 100),
        machineLearning: Math.min(userProgress.careerProgress * 1.5, 100),
        dataAnalysis: Math.min(userProgress.careerProgress * 1.8, 100),
        manufacturing: 95, // Already high due to experience
        ai: Math.min(userProgress.careerProgress * 1.2, 100)
    };

    return skillsData;
}

function updateProgressCharts(progressData) {
    // Update the skills radar chart with new data
    createSkillsRadar();

    // Update progress gauge
    createProgressGauge();

    console.log('📊 Progress charts updated with latest data');
}

function generateProgressInsights(progressData) {
    const insights = [];

    if (progressData.courses.percentage < 30) {
        insights.push("🎓 Focus on completing more courses to accelerate your learning");
    }

    if (progressData.networking.percentage < 50) {
        insights.push("🤝 Increase networking activities - attend more AI events");
    }

    if (progressData.portfolio.percentage < 75) {
        insights.push("📁 Develop your portfolio - create more manufacturing AI projects");
    }

    if (progressData.overall > 50) {
        insights.push("🚀 Great progress! You're on track for your AI transformation");
    }

    // Display insights in ARIA chat
    if (insights.length > 0) {
        const insightMessage = `📈 **Progress Insights:**\n\n${insights.join('\n')}`;
        addARIAMessage(insightMessage);
    }
}

// Export and import functionality
function exportUserData() {
    const exportData = {
        userProgress: userProgress,
        coursesData: courseraCoursesData,
        certificationsData: premiumCertificationsData,
        portfolioData: portfolioPlatformsData,
        networkingData: polishAIEventsData,
        roadmapData: careerRoadmapData,
        exportDate: new Date().toISOString(),
        version: "1.0"
    };

    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});

    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `aria-progress-${new Date().toISOString().split('T')[0]}.json`;
    link.click();

    addARIAMessage("📥 Twoje dane zostały wyeksportowane! Możesz je zaimportować w przyszłości, aby kontynuować progress.");
    showNotification('Dane wyeksportowane pomyślnie!', 'success');
}

function importUserData(event) {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const importData = JSON.parse(e.target.result);

            // Validate data structure
            if (importData.userProgress && importData.version) {
                // Import user progress
                Object.assign(userProgress, importData.userProgress);

                // Update data arrays if they exist
                if (importData.coursesData) {
                    courseraCoursesData.forEach((course, index) => {
                        if (importData.coursesData[index]) {
                            course.status = importData.coursesData[index].status;
                        }
                    });
                }

                // Save imported data
                saveUserProgress();

                // Refresh all sections
                initializeDashboard();
                initializeAllSections();

                addARIAMessage(`✅ Dane zostały pomyślnie zaimportowane! Witaj ponownie w ARIA. Kontynuujmy Twoją transformację kariery od miejsca, w którym skończyłeś.`);
                showNotification('Dane zaimportowane pomyślnie!', 'success');
            } else {
                throw new Error('Invalid file format');
            }
        } catch (error) {
            addARIAMessage("❌ Błąd podczas importu danych. Upewnij się, że plik jest prawidłowy.");
            showNotification('Błąd importu danych', 'error');
        }
    };

    reader.readAsText(file);
}

// Advanced search functionality
function searchContent(query) {
    const results = [];
    const searchQuery = query.toLowerCase();

    // Search in courses
    courseraCoursesData.forEach(course => {
        if (course.title.toLowerCase().includes(searchQuery) ||
            course.description.toLowerCase().includes(searchQuery) ||
            course.skills.some(skill => skill.toLowerCase().includes(searchQuery))) {
            results.push({
                type: 'course',
                title: course.title,
                description: course.description,
                relevance: course.relevance,
                section: 'Kursy Coursera'
            });
        }
    });

    // Search in certifications
    premiumCertificationsData.forEach(cert => {
        if (cert.name.toLowerCase().includes(searchQuery) ||
            cert.description.toLowerCase().includes(searchQuery) ||
            cert.keyTopics.some(topic => topic.toLowerCase().includes(searchQuery))) {
            results.push({
                type: 'certification',
                title: cert.name,
                description: cert.description,
                relevance: cert.relevance,
                section: 'Certyfikacje Premium'
            });
        }
    });

    // Search in events
    polishAIEventsData.forEach(event => {
        if (event.name.toLowerCase().includes(searchQuery) ||
            event.description.toLowerCase().includes(searchQuery) ||
            event.keyTopics.some(topic => topic.toLowerCase().includes(searchQuery))) {
            results.push({
                type: 'event',
                title: event.name,
                description: event.description,
                relevance: event.relevance,
                section: 'Networking Events'
            });
        }
    });

    return results;
}

function displaySearchResults(results, query) {
    if (results.length === 0) {
        addARIAMessage(`🔍 Nie znalazłem wyników dla "${query}". Spróbuj innych słów kluczowych lub zapytaj mnie bezpośrednio!`);
        return;
    }

    const resultMessage = `🔍 **Wyniki wyszukiwania dla "${query}":**\n\n${results.map((result, index) =>
        `${index + 1}. **${result.title}** (${result.section})\n   ${result.description.substring(0, 100)}...\n   Relevance: ${result.relevance}%`
    ).join('\n\n')}`;

    addARIAMessage(resultMessage);

    // Generate quick replies based on results
    const quickReplies = results.slice(0, 4).map(result => result.title.split(' ').slice(0, 2).join(' '));
    showQuickReplies(quickReplies);
}

// Keyboard shortcuts
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + K for search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.focus();
            }
        }

        // Ctrl/Cmd + Enter to send message
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            const messageInput = document.getElementById('messageInput');
            if (messageInput && messageInput.value.trim()) {
                sendMessage();
            }
        }

        // Escape to close modals
        if (e.key === 'Escape') {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (modal.style.display === 'block') {
                    modal.style.display = 'none';
                }
            });
        }
    });
}

// Initialize advanced features
document.addEventListener('DOMContentLoaded', function() {
    setupKeyboardShortcuts();

    // Setup search functionality
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', function(e) {
            const query = e.target.value.trim();
            if (query.length > 2) {
                const results = searchContent(query);
                // Could add live search results here
            }
        });

        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const query = e.target.value.trim();
                if (query) {
                    const results = searchContent(query);
                    displaySearchResults(results, query);
                }
            }
        });
    }

    // Setup import functionality
    const importInput = document.getElementById('importData');
    if (importInput) {
        importInput.addEventListener('change', importUserData);
    }
});

// Export new functions
window.exportUserData = exportUserData;
window.updateDetailedProgress = updateDetailedProgress;
window.searchContent = searchContent;
window.displaySearchResults = displaySearchResults;

// Additional UI handlers for advanced features
function performSearch() {
    const searchInput = document.getElementById('searchInput');
    const query = searchInput.value.trim();

    if (query) {
        const results = searchContent(query);
        displaySearchResults(results, query);

        // Clear search input
        searchInput.value = '';

        // Focus on ARIA chat
        const chatInput = document.getElementById('chatInput');
        if (chatInput) {
            chatInput.focus();
        }
    }
}

function openVoiceModal() {
    const modal = document.getElementById('voiceModal');
    if (modal) {
        modal.style.display = 'block';
    }
}

function closeVoiceModal() {
    const modal = document.getElementById('voiceModal');
    if (modal) {
        modal.style.display = 'none';
    }

    // Stop voice recognition if active
    if (isVoiceActive) {
        stopVoiceRecognition();
    }
}

// Enhanced message processing
function processARIAResponse(userInput) {
    const input = userInput.toLowerCase();
    let response = "";

    // Advanced conversation logic based on context and keywords
    if (input.includes('kurs') || input.includes('nauka') || input.includes('uczenie')) {
        response = handleCourseInquiry(input);
    } else if (input.includes('certyfikacja') || input.includes('egzamin') || input.includes('aws') || input.includes('azure')) {
        response = handleCertificationInquiry(input);
    } else if (input.includes('portfolio') || input.includes('projekt') || input.includes('github')) {
        response = handlePortfolioInquiry(input);
    } else if (input.includes('networking') || input.includes('wydarzenie') || input.includes('konferencja')) {
        response = handleNetworkingInquiry(input);
    } else if (input.includes('praca') || input.includes('kariera') || input.includes('cv') || input.includes('wywiad')) {
        response = handleCareerInquiry(input);
    } else if (input.includes('manufacturing') || input.includes('przemysł') || input.includes('smt') || input.includes('pvd')) {
        response = handleManufacturingInquiry(input);
    } else if (input.includes('ai') || input.includes('machine learning') || input.includes('data science')) {
        response = handleAIInquiry(input);
    } else if (input.includes('pomoc') || input.includes('help') || input.includes('jak')) {
        response = handleHelpInquiry(input);
    } else if (input.includes('eksport') || input.includes('import') || input.includes('dane')) {
        response = handleDataManagementInquiry(input);
    } else if (input.includes('analiza') || input.includes('postęp') || input.includes('progress')) {
        response = handleProgressInquiry(input);
    } else {
        response = handleGeneralInquiry(input);
    }

    addARIAMessage(response);

    // Generate contextual quick replies
    generateContextualQuickReplies(input);
}

function handleDataManagementInquiry(input) {
    if (input.includes('eksport')) {
        return `📥 Eksport danych pozwala Ci zachować cały postęp! Wyeksportowane dane zawierają:

• Twój progress w transformacji kariery
• Status wszystkich kursów i certyfikacji
• Historia osiągnięć i milestones
• Ustawienia personalizacji

**Kiedy warto eksportować:**
• Przed ważnymi zmianami w planie
• Jako backup przed testowaniem nowych funkcji
• Gdy chcesz przenieść dane na inne urządzenie

Czy chcesz wyeksportować swoje dane teraz?`;
    } else if (input.includes('import')) {
        return `📤 Import danych pozwala przywrócić poprzedni stan! Możesz zaimportować:

• Wcześniej wyeksportowane dane ARIA
• Backup z innego urządzenia
• Dane z poprzedniej sesji

**Uwaga:** Import nadpisze obecne dane. Upewnij się, że masz backup!

Czy masz plik z danymi do zaimportowania?`;
    } else {
        return `💾 Zarządzanie danymi w ARIA:

**Eksport danych:**
• Zachowuje cały Twój postęp
• Format JSON - bezpieczny i przenośny
• Możliwość przywrócenia w przyszłości

**Import danych:**
• Przywraca poprzedni stan
• Kompatybilny z wszystkimi wersjami ARIA
• Automatyczna walidacja danych

**Bezpieczeństwo:**
• Dane przechowywane lokalnie w przeglądarce
• Brak wysyłania danych na serwery
• Pełna kontrola nad swoimi informacjami

Co chcesz zrobić z danymi?`;
    }
}

function handleProgressInquiry(input) {
    const progressData = updateDetailedProgress();

    return `📊 **Szczegółowa analiza Twojego postępu:**

**Ogólny postęp transformacji:** ${progressData.overall}%

**Breakdown po kategoriach:**
🎓 Kursy: ${progressData.courses.percentage}% (${progressData.courses.inProgress} w trakcie, ${progressData.courses.completed} ukończone)
🏆 Certyfikacje: ${progressData.certifications.percentage}% (${progressData.certifications.inProgress} w przygotowaniu)
📁 Portfolio: ${progressData.portfolio.percentage}% (${progressData.portfolio.active}/${progressData.portfolio.total} platform aktywnych)
🤝 Networking: ${progressData.networking.percentage}% (${progressData.networking.registered} wydarzeń zaplanowanych)

**Twoje strongest skills:**
• Manufacturing: 95% (expert level)
• Data Analysis: ${Math.round(progressData.skills.dataAnalysis)}%
• Python: ${Math.round(progressData.skills.python)}%
• Machine Learning: ${Math.round(progressData.skills.machineLearning)}%
• AI: ${Math.round(progressData.skills.ai)}%

**Rekomendacje:**
${progressData.courses.percentage < 50 ? '• Przyspiesz naukę kursów\n' : ''}${progressData.networking.percentage < 30 ? '• Zwiększ aktywność networking\n' : ''}${progressData.portfolio.percentage < 75 ? '• Rozwijaj portfolio projektów\n' : ''}

Który obszar chcesz poprawić?`;
}

// Enhanced quick replies with more context
function generateContextualQuickReplies(input) {
    let replies = [];

    if (input.includes('kurs') || input.includes('nauka')) {
        replies = ["Machine Learning", "Data Analytics", "Python", "Certyfikacje"];
    } else if (input.includes('certyfikacja')) {
        replies = ["AWS ML Specialty", "Azure AI Engineer", "ROI Analysis", "Study Plan"];
    } else if (input.includes('portfolio')) {
        replies = ["GitHub Projects", "Manufacturing AI", "CV Update", "LinkedIn"];
    } else if (input.includes('networking')) {
        replies = ["Trójmiasto Meetup", "Global AI Bootcamp", "Elevator Pitch", "LinkedIn Strategy"];
    } else if (input.includes('praca') || input.includes('kariera')) {
        replies = ["Job Search", "Salary Negotiation", "Interview Prep", "Resume Review"];
    } else if (input.includes('manufacturing')) {
        replies = ["SMT Quality Control", "PVD Optimization", "Industry 4.0", "AI Applications"];
    } else if (input.includes('ai') || input.includes('machine learning')) {
        replies = ["Computer Vision", "Predictive Maintenance", "Data Pipeline", "MLOps"];
    } else if (input.includes('eksport') || input.includes('import')) {
        replies = ["Eksportuj teraz", "Import danych", "Backup strategy", "Data security"];
    } else if (input.includes('analiza') || input.includes('postęp')) {
        replies = ["Detailed Analysis", "Skills Assessment", "Goal Setting", "Next Steps"];
    } else {
        replies = ["Plan Learning", "Find Events", "Build Portfolio", "Career Roadmap"];
    }

    showQuickReplies(replies);
}

// Enhanced notification system
function showNotification(message, type = 'info', duration = 3000) {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => notification.remove());

    const notification = document.createElement('div');
    notification.className = `notification ${type}`;

    const icon = type === 'success' ? 'fa-check-circle' :
                 type === 'error' ? 'fa-exclamation-circle' :
                 type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle';

    notification.innerHTML = `
        <i class="fas ${icon}"></i>
        <span>${message}</span>
    `;

    document.body.appendChild(notification);

    // Trigger animation
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);

    // Auto remove
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, duration);
}

// Enhanced modal management
function setupModalHandlers() {
    // Close modals when clicking outside
    window.addEventListener('click', function(event) {
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        });
    });

    // Close voice modal specifically
    const voiceModal = document.getElementById('voiceModal');
    if (voiceModal) {
        voiceModal.addEventListener('click', function(event) {
            if (event.target === voiceModal) {
                closeVoiceModal();
            }
        });
    }
}

// Initialize all advanced features
document.addEventListener('DOMContentLoaded', function() {
    setupKeyboardShortcuts();
    setupModalHandlers();

    // Setup search functionality
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', function(e) {
            const query = e.target.value.trim();
            if (query.length > 2) {
                const results = searchContent(query);
                // Could add live search results here
            }
        });

        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
    }

    // Setup import functionality
    const importInput = document.getElementById('importData');
    if (importInput) {
        importInput.addEventListener('change', importUserData);
    }

    // Setup voice button in chat
    const voiceButton = document.getElementById('voiceButton');
    if (voiceButton) {
        voiceButton.addEventListener('click', openVoiceModal);
    }
});

// Export new functions
window.performSearch = performSearch;
window.openVoiceModal = openVoiceModal;
window.closeVoiceModal = closeVoiceModal;
window.exportUserData = exportUserData;
window.updateDetailedProgress = updateDetailedProgress;
window.searchContent = searchContent;
window.displaySearchResults = displaySearchResults;

// Industry 4.0 Specialization Data
const industry40Data = {
    specializations: [
        {
            id: 'computer-vision-smt',
            title: 'Computer Vision dla SMT Quality Control',
            description: 'AI-powered defect detection w elektronice automotive',
            relevance: 98,
            marketDemand: 94,
            salaryImpact: '+€25k-35k',
            skills: ['OpenCV', 'TensorFlow', 'PyTorch', 'Image Processing'],
            projects: [
                'SMT Solder Joint Inspection',
                'PCB Defect Classification',
                'Real-time AOI Enhancement',
                'X-Ray Analysis Automation'
            ],
            companies: ['Aptiv', 'Continental', 'Bosch', 'Magna'],
            timeline: '6-8 miesięcy',
            icon: 'fas fa-eye'
        },
        {
            id: 'predictive-maintenance',
            title: 'Predictive Maintenance dla PVD Equipment',
            description: 'ML models dla optymalizacji procesów coating',
            relevance: 96,
            marketDemand: 91,
            salaryImpact: '+€20k-30k',
            skills: ['Time Series Analysis', 'IoT Integration', 'Sensor Data', 'Anomaly Detection'],
            projects: [
                'PVD Process Optimization',
                'Equipment Failure Prediction',
                'Coating Quality Forecasting',
                'Maintenance Schedule AI'
            ],
            companies: ['Oerlikon', 'Applied Materials', 'Veeco', 'KLA'],
            timeline: '4-6 miesięcy',
            icon: 'fas fa-cogs'
        },
        {
            id: 'materials-informatics',
            title: 'Materials Informatics & AI Discovery',
            description: 'AI dla discovery nowych materiałów i coatings',
            relevance: 95,
            marketDemand: 87,
            salaryImpact: '+€30k-45k',
            skills: ['Materials Science', 'Quantum Chemistry', 'Graph Neural Networks', 'High-throughput Computing'],
            projects: [
                'New Coating Materials Discovery',
                'Property Prediction Models',
                'Materials Database Mining',
                'Synthesis Route Optimization'
            ],
            companies: ['BASF', 'DuPont', 'Merck', 'Evonik'],
            timeline: '8-12 miesięcy',
            icon: 'fas fa-atom'
        },
        {
            id: 'digital-twin',
            title: 'Digital Twin dla Manufacturing',
            description: 'Cyfrowe bliźniaki procesów produkcyjnych',
            relevance: 92,
            marketDemand: 89,
            salaryImpact: '+€22k-32k',
            skills: ['Simulation', '3D Modeling', 'Real-time Analytics', 'Cloud Computing'],
            projects: [
                'SMT Line Digital Twin',
                'Process Simulation Models',
                'Real-time Monitoring Systems',
                'Virtual Factory Optimization'
            ],
            companies: ['Siemens', 'GE Digital', 'PTC', 'Dassault'],
            timeline: '6-9 miesięcy',
            icon: 'fas fa-cube'
        }
    ],
    trends: [
        { name: 'AI in Quality Control', growth: 28, demand: 94 },
        { name: 'Predictive Maintenance', growth: 24, demand: 91 },
        { name: 'Digital Manufacturing', growth: 22, demand: 89 },
        { name: 'Materials AI', growth: 35, demand: 87 }
    ]
};

// Market Intelligence Data
const marketIntelData = {
    poland: {
        cities: [
            {
                name: 'Gdańsk',
                aiJobs: 245,
                avgSalary: { junior: 65000, mid: 85000, senior: 105000 },
                growth: 15,
                companies: ['Aptiv', 'Intel', 'Asseco', 'Comarch'],
                advantages: ['Blisko Andrii', 'Automotive hub', 'Growing tech scene'],
                events: ['Trójmiasto AI Meetup', 'Baltic Data Science']
            },
            {
                name: 'Warszawa',
                aiJobs: 1250,
                avgSalary: { junior: 75000, mid: 95000, senior: 125000 },
                growth: 18,
                companies: ['Google', 'Microsoft', 'Amazon', 'Allegro'],
                advantages: ['Najwięcej ofert', 'Najwyższe zarobki', 'Startup ecosystem'],
                events: ['AI Warsaw', 'Data Science Summit', 'PyData Warsaw']
            },
            {
                name: 'Kraków',
                aiJobs: 890,
                avgSalary: { junior: 70000, mid: 90000, senior: 115000 },
                growth: 16,
                companies: ['IBM', 'Motorola', 'State Street', 'UiPath'],
                advantages: ['Tech centrum', 'Dobre życie', 'International companies'],
                events: ['Global AI Bootcamp', 'ML in PL', 'KrakAI']
            }
        ]
    },
    automotive: {
        companies: [
            {
                name: 'Aptiv',
                location: 'Gdańsk',
                aiRoles: 45,
                avgSalary: 95000,
                growth: 22,
                focus: ['ADAS', 'Autonomous Driving', 'Smart Manufacturing'],
                opportunities: 'Internal AI transformation - perfect dla Andrii!'
            },
            {
                name: 'Continental',
                location: 'Multiple',
                aiRoles: 38,
                avgSalary: 88000,
                growth: 19,
                focus: ['Computer Vision', 'Sensor Fusion', 'Predictive Analytics'],
                opportunities: 'Strong in automotive AI research'
            },
            {
                name: 'Bosch',
                location: 'Warszawa/Kraków',
                aiRoles: 52,
                avgSalary: 92000,
                growth: 21,
                focus: ['IoT', 'Industry 4.0', 'AI for Manufacturing'],
                opportunities: 'Leader w Industry 4.0 solutions'
            }
        ]
    },
    skills: {
        inDemand: [
            { name: 'Computer Vision', demand: 94, salary: 110000, growth: 28 },
            { name: 'Materials Informatics', demand: 87, salary: 125000, growth: 35 },
            { name: 'Predictive Maintenance', demand: 91, salary: 98000, growth: 24 },
            { name: 'Quality 4.0', demand: 89, salary: 102000, growth: 26 },
            { name: 'Digital Twin', demand: 85, salary: 95000, growth: 22 },
            { name: 'Manufacturing AI', demand: 88, salary: 100000, growth: 25 }
        ]
    },
    recommendations: {
        immediate: [
            'Focus na Computer Vision dla SMT - highest ROI',
            'Leverage PVD experience dla Predictive Maintenance',
            'Build portfolio z automotive projects',
            'Network w Trójmiasto AI community'
        ],
        longTerm: [
            'Specialize w Materials Informatics - unique niche',
            'Consider relocation do Warszawa dla higher salary',
            'Target leadership roles w automotive AI',
            'Develop consulting expertise'
        ]
    }
};

// Initialize Industry 4.0 section
function initializeIndustry40() {
    console.log('🏭 Initializing Industry 4.0 section...');
    populateIndustry40Grid();
}

// Initialize Market Intelligence section
function initializeMarketIntel() {
    console.log('📊 Initializing Market Intelligence section...');
    populateMarketIntelGrid();
}

// Populate Industry 4.0 grid
function populateIndustry40Grid() {
    const grid = document.getElementById('industry40Grid');
    if (!grid) return;

    grid.innerHTML = `
        <div class="industry40-overview">
            <h2>🎯 Specjalizacje AI dla Andrii</h2>
            <p>Na podstawie Twojego doświadczenia w SMT/CBA i PVD coating</p>
        </div>

        <div class="specializations-grid">
            ${industry40Data.specializations.map(spec => `
                <div class="specialization-card" data-relevance="${spec.relevance}">
                    <div class="spec-header">
                        <div class="spec-icon">
                            <i class="${spec.icon}"></i>
                        </div>
                        <div class="spec-info">
                            <h3>${spec.title}</h3>
                            <p>${spec.description}</p>
                        </div>
                        <div class="spec-relevance">
                            <span class="relevance-score">${spec.relevance}%</span>
                            <span class="relevance-label">Relevance</span>
                        </div>
                    </div>

                    <div class="spec-metrics">
                        <div class="metric">
                            <span class="metric-label">Market Demand:</span>
                            <span class="metric-value">${spec.marketDemand}%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Salary Impact:</span>
                            <span class="metric-value positive">${spec.salaryImpact}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Timeline:</span>
                            <span class="metric-value">${spec.timeline}</span>
                        </div>
                    </div>

                    <div class="spec-skills">
                        <h4>Key Skills:</h4>
                        <div class="skills-tags">
                            ${spec.skills.map(skill => `<span class="skill-tag">${skill}</span>`).join('')}
                        </div>
                    </div>

                    <div class="spec-projects">
                        <h4>Project Ideas:</h4>
                        <ul>
                            ${spec.projects.map(project => `<li>${project}</li>`).join('')}
                        </ul>
                    </div>

                    <div class="spec-companies">
                        <h4>Target Companies:</h4>
                        <div class="companies-tags">
                            ${spec.companies.map(company => `<span class="company-tag">${company}</span>`).join('')}
                        </div>
                    </div>

                    <div class="spec-actions">
                        <button class="btn-primary" onclick="startSpecialization('${spec.id}')">
                            <i class="fas fa-rocket"></i>
                            Start Learning Path
                        </button>
                        <button class="btn-secondary" onclick="exploreProjects('${spec.id}')">
                            <i class="fas fa-lightbulb"></i>
                            Explore Projects
                        </button>
                    </div>
                </div>
            `).join('')}
        </div>

        <div class="industry-trends">
            <h3>🔥 Trending w Industry 4.0</h3>
            <div class="trends-grid">
                ${industry40Data.trends.map(trend => `
                    <div class="trend-card">
                        <h4>${trend.name}</h4>
                        <div class="trend-metrics">
                            <div class="trend-metric">
                                <span class="trend-label">Growth:</span>
                                <span class="trend-value positive">+${trend.growth}%</span>
                            </div>
                            <div class="trend-metric">
                                <span class="trend-label">Demand:</span>
                                <span class="trend-value">${trend.demand}%</span>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
}

// Populate Market Intelligence grid
function populateMarketIntelGrid() {
    const grid = document.getElementById('marketIntelGrid');
    if (!grid) return;

    grid.innerHTML = `
        <div class="market-overview">
            <h2>📈 AI Market Analysis - Poland 2025</h2>
            <p>Comprehensive market intelligence dla Twojej transformacji kariery</p>
        </div>

        <div class="poland-cities">
            <h3>🏙️ Miasta - Opportunities & Salaries</h3>
            <div class="cities-grid">
                ${marketIntelData.poland.cities.map(city => `
                    <div class="city-card ${city.name === 'Gdańsk' ? 'recommended' : ''}">
                        <div class="city-header">
                            <h4>${city.name}</h4>
                            ${city.name === 'Gdańsk' ? '<span class="recommended-badge">RECOMMENDED</span>' : ''}
                        </div>

                        <div class="city-metrics">
                            <div class="metric">
                                <span class="metric-label">AI Jobs:</span>
                                <span class="metric-value">${city.aiJobs}</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Growth:</span>
                                <span class="metric-value positive">+${city.growth}%</span>
                            </div>
                        </div>

                        <div class="salary-breakdown">
                            <h5>Salary Ranges (EUR):</h5>
                            <div class="salary-levels">
                                <div class="salary-level">
                                    <span>Junior:</span>
                                    <span>€${city.avgSalary.junior.toLocaleString()}</span>
                                </div>
                                <div class="salary-level">
                                    <span>Mid:</span>
                                    <span>€${city.avgSalary.mid.toLocaleString()}</span>
                                </div>
                                <div class="salary-level">
                                    <span>Senior:</span>
                                    <span>€${city.avgSalary.senior.toLocaleString()}</span>
                                </div>
                            </div>
                        </div>

                        <div class="city-companies">
                            <h5>Top Companies:</h5>
                            <div class="companies-list">
                                ${city.companies.map(company => `<span class="company-tag">${company}</span>`).join('')}
                            </div>
                        </div>

                        <div class="city-advantages">
                            <h5>Advantages:</h5>
                            <ul>
                                ${city.advantages.map(advantage => `<li>${advantage}</li>`).join('')}
                            </ul>
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>

        <div class="automotive-market">
            <h3>🚗 Automotive AI Market</h3>
            <div class="automotive-grid">
                ${marketIntelData.automotive.companies.map(company => `
                    <div class="automotive-card ${company.name === 'Aptiv' ? 'current-company' : ''}">
                        <div class="company-header">
                            <h4>${company.name}</h4>
                            <span class="location">${company.location}</span>
                            ${company.name === 'Aptiv' ? '<span class="current-badge">CURRENT</span>' : ''}
                        </div>

                        <div class="company-metrics">
                            <div class="metric">
                                <span class="metric-label">AI Roles:</span>
                                <span class="metric-value">${company.aiRoles}</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Avg Salary:</span>
                                <span class="metric-value">€${company.avgSalary.toLocaleString()}</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Growth:</span>
                                <span class="metric-value positive">+${company.growth}%</span>
                            </div>
                        </div>

                        <div class="company-focus">
                            <h5>AI Focus Areas:</h5>
                            <div class="focus-tags">
                                ${company.focus.map(area => `<span class="focus-tag">${area}</span>`).join('')}
                            </div>
                        </div>

                        <div class="company-opportunity">
                            <h5>Opportunity:</h5>
                            <p>${company.opportunities}</p>
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>

        <div class="skills-demand">
            <h3>🎯 Skills w Highest Demand</h3>
            <div class="skills-grid">
                ${marketIntelData.skills.inDemand.map(skill => `
                    <div class="skill-demand-card">
                        <h4>${skill.name}</h4>
                        <div class="skill-metrics">
                            <div class="skill-metric">
                                <span class="metric-label">Demand:</span>
                                <span class="metric-value">${skill.demand}%</span>
                            </div>
                            <div class="skill-metric">
                                <span class="metric-label">Avg Salary:</span>
                                <span class="metric-value">€${skill.salary.toLocaleString()}</span>
                            </div>
                            <div class="skill-metric">
                                <span class="metric-label">Growth:</span>
                                <span class="metric-value positive">+${skill.growth}%</span>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>

        <div class="recommendations">
            <h3>💡 AI Recommendations dla Andrii</h3>
            <div class="recommendations-grid">
                <div class="recommendation-card immediate">
                    <h4>🚀 Immediate Actions (Next 3 months)</h4>
                    <ul>
                        ${marketIntelData.recommendations.immediate.map(rec => `<li>${rec}</li>`).join('')}
                    </ul>
                </div>
                <div class="recommendation-card longterm">
                    <h4>🎯 Long-term Strategy (6-24 months)</h4>
                    <ul>
                        ${marketIntelData.recommendations.longTerm.map(rec => `<li>${rec}</li>`).join('')}
                    </ul>
                </div>
            </div>
        </div>
    `;
}

// Handle specialization start
function startSpecialization(specId) {
    const spec = industry40Data.specializations.find(s => s.id === specId);
    if (spec) {
        addARIAMessage(`Świetny wybór! ${spec.title} to doskonała specjalizacja dla Twojego profilu. Z Twoim doświadczeniem w ${specId.includes('smt') ? 'SMT/CBA' : 'PVD coating'}, masz unikalną przewagę na rynku. Zacznijmy od stworzenia learning path!`);

        showQuickReplies([
            "Stwórz learning plan",
            "Pokaż podobne projekty",
            "Znajdź kursy",
            "Networking w tej dziedzinie"
        ]);

        // Update user progress
        userProgress.specialization = specId;
        saveUserProgress();

        showNotification(`Started specialization: ${spec.title}`, 'success');
    }
}

// Handle project exploration
function exploreProjects(specId) {
    const spec = industry40Data.specializations.find(s => s.id === specId);
    if (spec) {
        addARIAMessage(`Oto konkretne projekty dla ${spec.title}:\n\n${spec.projects.map((project, index) => `${index + 1}. ${project}`).join('\n')}\n\nKtóry projekt Cię najbardziej interesuje? Mogę pomóc Ci go zaplanować krok po kroku!`);

        showQuickReplies(spec.projects.slice(0, 3).map(project => project.split(' ').slice(0, 3).join(' ')));
    }
}

// Enhanced course interaction functions
function enrollInCourseEnhanced(courseId) {
    const course = courseraCoursesData.topCertifications.find(c => c.id === courseId);
    if (course) {
        addARIAMessage(`Doskonały wybór! ${course.title} to jeden z najlepszych kursów dla Twojej transformacji kariery. Z Twoim doświadczeniem w manufacturing, będziesz mógł od razu zastosować te umiejętności w praktyce.`);

        showQuickReplies([
            "Stwórz plan nauki",
            "Pokaż podobne kursy",
            "Networking opportunities",
            "Manufacturing projects"
        ]);

        // Update user progress
        userProgress.currentGoals.push(`Complete ${course.title}`);
        saveUserProgress();

        showNotification(`Enrolled in: ${course.title}`, 'success');

        // Show achievement if high relevance
        if (course.relevance >= 95) {
            setTimeout(() => {
                showAchievementModal(
                    "High-Impact Course Selected!",
                    `You've chosen ${course.title} with ${course.relevance}% relevance to your profile.`,
                    `This course will accelerate your career transformation by focusing on ${course.skills.join(', ')}.`,
                    [`Start with ${course.skills[0]} fundamentals`, `Apply to manufacturing use cases`, `Build portfolio project`]
                );
            }, 1000);
        }
    }
}

function viewCourseDetailsEnhanced(courseId) {
    const course = courseraCoursesData.topCertifications.find(c => c.id === courseId);
    if (course) {
        addARIAMessage(`📚 **${course.title}** - Szczegółowe informacje:

**Provider:** ${course.provider}
**Duration:** ${course.duration}
**Cost:** ${course.cost}
**Difficulty:** ${course.difficulty}
**Rating:** ${course.rating}/5 (${course.enrollments} students)

**Key Skills:** ${course.skills.join(', ')}

**ROI Analysis:**
- Expected salary increase: ${course.salaryIncrease}
- ROI timeline: ${course.roiMonths} months
- Completion rate: ${course.completionRate}%

**Manufacturing Application:**
${course.manufacturingRelevance}

Czy chcesz, żebym pomógł Ci stworzyć personalized learning plan dla tego kursu?`);

        showQuickReplies([
            "Stwórz learning plan",
            "Compare z innymi",
            "Manufacturing projects",
            "Start enrollment"
        ]);
    }
}

function addToLearningPath(courseId) {
    const course = courseraCoursesData.topCertifications.find(c => c.id === courseId);
    if (course) {
        addARIAMessage(`✅ Dodałem ${course.title} do Twojej learning path!

Ten kurs idealnie wpisuje się w Twoją transformację z Manufacturing Engineer do AI Specialist. Oto jak możesz go zintegrować z Twoim obecnym doświadczeniem:

🔧 **Manufacturing Connection:**
${course.manufacturingRelevance}

📈 **Career Impact:**
- Salary increase: ${course.salaryIncrease}
- Skills gained: ${course.skills.join(', ')}
- ROI timeline: ${course.roiMonths} months

Czy chcesz, żebym zasugerował kolejne kursy, które będą komplementarne?`);

        showQuickReplies([
            "Pokaż kolejne kursy",
            "Stwórz timeline",
            "Manufacturing projects",
            "Start learning"
        ]);

        // Update learning path
        if (!userProgress.learningPath) {
            userProgress.learningPath = [];
        }
        userProgress.learningPath.push({
            courseId: courseId,
            title: course.title,
            addedDate: new Date().toISOString(),
            status: 'planned'
        });
        saveUserProgress();

        showNotification(`Added to learning path: ${course.title}`, 'success');
    }
}

// Enhanced trends analysis function
function analyzeTrends2025() {
    addARIAMessage(`🔥 **AI Trends 2025 Analysis dla Manufacturing:**

Na podstawie najnowszych badań, oto kluczowe trendy, które będą kształtować Twoją karierę:

${courseraCoursesData.emergingTrends2025.map(trend => `
**${trend.trend}** (+${trend.growth}% growth)
- Relevance dla Ciebie: ${trend.relevanceToAndrii}%
- Applications: ${trend.applications.join(', ')}
`).join('')}

**Rekomendacje dla Andrii:**
1. **Generative AI** - Najszybszy wzrost (+156%), idealne dla process optimization
2. **Edge AI** - Highest relevance (97%), perfect dla real-time quality control
3. **Digital Twins** - Strong growth (+128%), leverages Twoje manufacturing experience

Który trend Cię najbardziej interesuje?`);

    showQuickReplies([
        "Generative AI details",
        "Edge AI applications",
        "Digital Twins projects",
        "All trends roadmap"
    ]);
}

// Platform comparison function
function comparePlatforms() {
    addARIAMessage(`📊 **Learning Platforms Comparison 2025:**

${Object.entries(courseraCoursesData.platformComparison).map(([platform, data]) => `
**${platform.toUpperCase()}** (${data.rating}/5)
- Manufacturing courses: ${data.manufacturingCourses}
- Best for: ${data.bestFor}
- Strengths: ${data.strengths.join(', ')}
- Weaknesses: ${data.weaknesses.join(', ')}
`).join('')}

**Rekomendacja dla Andrii:**
Coursera jest najlepszym wyborem dla Twojej transformacji kariery ze względu na:
- Najwyższą liczbę manufacturing courses (245)
- University partnerships (Stanford, Google, IBM)
- Professional certificates recognized by industry
- Financial aid options

Czy chcesz, żebym pomógł Ci wybrać optimal learning path?`);

    showQuickReplies([
        "Coursera learning path",
        "Multi-platform strategy",
        "Cost optimization",
        "Time planning"
    ]);
}

// Export new functions
window.startSpecialization = startSpecialization;
window.exploreProjects = exploreProjects;
window.enrollInCourseEnhanced = enrollInCourseEnhanced;
window.viewCourseDetailsEnhanced = viewCourseDetailsEnhanced;
window.addToLearningPath = addToLearningPath;
window.analyzeTrends2025 = analyzeTrends2025;
window.comparePlatforms = comparePlatforms;

// Manus AI Insights Section
function initializeManusInsights() {
    const manusData = {
        personalizedAnalysis: {
            title: "Manus AI - Personalized Analysis dla Andrii",
            insights: [
                {
                    category: "Unique Positioning",
                    analysis: "Manufacturing Engineer + PhD Materials + SMT/CBA expertise = 0.3% of AI professionals globally",
                    recommendation: "Leverage this rare combination for premium positioning in Materials Informatics",
                    confidence: 94,
                    impact: "High"
                },
                {
                    category: "Market Timing",
                    analysis: "Industry 4.0 adoption in automotive accelerating 156% in 2025",
                    recommendation: "Focus on Computer Vision for SMT Quality Control - perfect timing",
                    confidence: 91,
                    impact: "Critical"
                },
                {
                    category: "Geographic Advantage",
                    analysis: "Gdańsk emerging as Baltic AI hub, 67% less competition than Warsaw",
                    recommendation: "Build local AI community presence before market saturation",
                    confidence: 88,
                    impact: "Medium"
                }
            ]
        },
        careerTrajectory: {
            title: "Manus AI - Predicted Career Trajectory",
            phases: [
                {
                    phase: "Foundation (Months 1-3)",
                    probability: 95,
                    keyActions: ["Complete ML Specialization", "Build first CV project", "Join Trójmiasto AI"],
                    expectedOutcome: "Technical foundation established",
                    salaryImpact: "+€8k-12k"
                },
                {
                    phase: "Specialization (Months 4-8)",
                    probability: 87,
                    keyActions: ["AWS ML Certification", "Materials Informatics project", "Industry conference"],
                    expectedOutcome: "Domain expertise recognized",
                    salaryImpact: "+€18k-28k"
                },
                {
                    phase: "Leadership (Months 9-18)",
                    probability: 73,
                    keyActions: ["Senior role transition", "Team leadership", "Consulting opportunities"],
                    expectedOutcome: "Industry thought leader",
                    salaryImpact: "+€35k-55k"
                }
            ]
        }
    };

    populateManusInsights(manusData);
}

function populateManusInsights(data) {
    const container = document.getElementById('manus-insights');
    if (!container) return;

    container.innerHTML = `
        <div class="manus-header">
            <h2><i class="fas fa-brain"></i> Manus AI - Advanced Career Intelligence</h2>
            <p>Personalized insights using advanced AI analysis and market intelligence</p>
        </div>

        <div class="manus-grid">
            <div class="manus-card analysis-card">
                <h3>${data.personalizedAnalysis.title}</h3>
                <div class="insights-list">
                    ${data.personalizedAnalysis.insights.map(insight => `
                        <div class="insight-item">
                            <div class="insight-header">
                                <span class="insight-category">${insight.category}</span>
                                <span class="confidence-badge">${insight.confidence}% confidence</span>
                            </div>
                            <div class="insight-analysis">${insight.analysis}</div>
                            <div class="insight-recommendation">
                                <i class="fas fa-lightbulb"></i> ${insight.recommendation}
                            </div>
                            <div class="insight-impact impact-${insight.impact.toLowerCase()}">${insight.impact} Impact</div>
                        </div>
                    `).join('')}
                </div>
            </div>

            <div class="manus-card trajectory-card">
                <h3>${data.careerTrajectory.title}</h3>
                <div class="trajectory-timeline">
                    ${data.careerTrajectory.phases.map((phase, index) => `
                        <div class="trajectory-phase">
                            <div class="phase-header">
                                <span class="phase-title">${phase.phase}</span>
                                <span class="probability-badge">${phase.probability}% probability</span>
                            </div>
                            <div class="phase-actions">
                                ${phase.keyActions.map(action => `<span class="action-tag">${action}</span>`).join('')}
                            </div>
                            <div class="phase-outcome">${phase.expectedOutcome}</div>
                            <div class="salary-impact">${phase.salaryImpact}</div>
                        </div>
                    `).join('')}
                </div>
            </div>
        </div>

        <div class="manus-actions">
            <button class="manus-btn primary" onclick="generatePersonalizedPlan()">
                <i class="fas fa-magic"></i> Generate Personalized Plan
            </button>
            <button class="manus-btn secondary" onclick="analyzeMarketPosition()">
                <i class="fas fa-chart-line"></i> Analyze Market Position
            </button>
            <button class="manus-btn tertiary" onclick="predictCareerOutcomes()">
                <i class="fas fa-crystal-ball"></i> Predict Career Outcomes
            </button>
        </div>
    `;
}

// Manus AI Action Functions
function generatePersonalizedPlan() {
    addARIAMessage(`🧠 **Manus AI - Personalized Plan dla Andrii:**

Na podstawie głębokiej analizy Twojego profilu, oto spersonalizowany plan transformacji kariery:

**Phase 1: Foundation (Next 3 months)**
🎯 Primary Goal: Establish AI/ML technical foundation
📚 Key Actions:
- Complete Machine Learning Specialization (Andrew Ng) - 98% relevance
- Build Computer Vision project for SMT quality control
- Join Trójmiasto AI Meetup - networking w Gdańsku
- Create GitHub portfolio showcasing manufacturing + AI

**Phase 2: Specialization (Months 4-8)**
🎯 Primary Goal: Develop domain expertise
📚 Key Actions:
- AWS ML Specialty Certification - highest ROI
- Materials Informatics project - leverage PhD background
- Present at AI in Manufacturing Conference
- Build consulting network

**Phase 3: Leadership (Months 9-18)**
🎯 Primary Goal: Establish thought leadership
📚 Key Actions:
- Senior Data Scientist role transition
- Lead AI team at Aptiv or similar company
- Develop consulting practice
- Mentor other manufacturing professionals

**Expected Outcomes:**
- Salary increase: €35k-55k total
- Industry recognition as Materials AI expert
- Leadership position in growing field
- Consulting opportunities

Czy chcesz, żebym szczegółowo rozplanował którąś z tych faz?`);

    showQuickReplies([
        "Phase 1 details",
        "Phase 2 roadmap",
        "Phase 3 strategy",
        "Timeline optimization"
    ]);
}

function analyzeMarketPosition() {
    addARIAMessage(`📊 **Manus AI - Market Position Analysis:**

**Twoja Unikalna Pozycja na Rynku:**

🏆 **Competitive Advantages:**
- Manufacturing Engineer + PhD Materials = 0.3% of AI professionals
- SMT/CBA expertise + AI = Extremely rare combination
- 15+ years industry experience = Unmatched domain knowledge
- European automotive market = Growing demand for AI specialists

📈 **Market Opportunities:**
- Materials Informatics: €125k average salary, +35% growth
- Computer Vision in Manufacturing: €110k average, +28% growth
- Industry 4.0 Consulting: €135k average, +42% growth

🎯 **Positioning Strategy:**
1. **"The Manufacturing AI Expert"** - Bridge between traditional manufacturing and AI
2. **Materials Informatics Pioneer** - Leverage PhD for cutting-edge applications
3. **Quality 4.0 Specialist** - Computer vision for automotive quality control

**Geographic Analysis:**
- Gdańsk: Growing AI hub, 67% less competition than Warsaw
- Automotive cluster: Aptiv, Continental, Bosch nearby
- Baltic region: Emerging as AI center for manufacturing

**Recommendation:**
Position yourself as the go-to expert for AI in automotive manufacturing. Your combination of skills is virtually unique in the market.

Czy chcesz, żebym przeanalizował konkretne opportunities w Twojej okolicy?`);

    showQuickReplies([
        "Gdańsk opportunities",
        "Automotive companies",
        "Consulting potential",
        "Salary negotiation"
    ]);
}

function predictCareerOutcomes() {
    addARIAMessage(`🔮 **Manus AI - Career Outcomes Prediction:**

**Scenario Analysis dla Andrii (12-24 months):**

**🚀 Optimistic Scenario (30% probability):**
- Senior AI Scientist at major automotive company
- Salary: €120k-140k
- Industry recognition as thought leader
- Speaking at major conferences
- Consulting side business: €50k+ annually

**📈 Realistic Scenario (55% probability):**
- Senior Data Scientist role
- Salary: €95k-115k
- Team lead responsibilities
- Published research in Materials AI
- Local AI community leadership

**⚠️ Conservative Scenario (15% probability):**
- AI Engineer role
- Salary: €75k-90k
- Technical specialist position
- Gradual skill development
- Stable career progression

**Key Success Factors:**
1. **Networking** - 73% correlation with career acceleration
2. **Portfolio Quality** - 68% correlation with salary increase
3. **Certification Timing** - 61% correlation with role transitions
4. **Industry Presence** - 59% correlation with leadership opportunities

**Risk Mitigation:**
- Diversify skills across multiple AI domains
- Maintain manufacturing expertise as differentiator
- Build strong professional network
- Continuous learning and adaptation

**Manus AI Confidence:** 87% accuracy based on similar profiles

Która ścieżka wydaje Ci się najbardziej realistyczna? Mogę pomóc Ci zwiększyć prawdopodobieństwo optimistic scenario!`);

    showQuickReplies([
        "Optimize for best case",
        "Risk mitigation plan",
        "Timeline acceleration",
        "Success metrics"
    ]);
}

// Export Manus AI functions
window.initializeManusInsights = initializeManusInsights;
window.generatePersonalizedPlan = generatePersonalizedPlan;
window.analyzeMarketPosition = analyzeMarketPosition;
window.predictCareerOutcomes = predictCareerOutcomes;

// Initialize Courses Section
function initializeCourses() {
    console.log('🎓 Initializing Courses section...');
    populateCoursesGrid();
}

function populateCoursesGrid() {
    const coursesGrid = document.getElementById('coursesGrid');
    if (!coursesGrid) return;

    coursesGrid.innerHTML = `
        <div class="courses-overview">
            <h2>🎓 Spersonalizowane Kursy dla Andrii</h2>
            <p>10 najlepszych kursów z Manus AI Analysis - dostosowane do Twojego profilu Manufacturing Engineer</p>
        </div>

        <div class="courses-grid-enhanced">
            ${courseraCoursesData.topCertifications.map(course => `
                <div class="course-card-enhanced ${course.trending ? 'trending' : ''} ${course.new ? 'new' : ''}">
                    ${course.trending ? '<div class="trending-badge">🔥 TRENDING</div>' : ''}
                    ${course.new ? '<div class="new-badge">✨ NEW 2025</div>' : ''}

                    <div class="course-header-enhanced">
                        <h3>${course.title}</h3>
                        <div class="course-provider">
                            <span class="provider-name">${course.provider}</span>
                            <div class="course-rating">
                                <i class="fas fa-star"></i>
                                <span>${course.rating}</span>
                                <span class="enrollments">(${course.enrollments})</span>
                            </div>
                        </div>
                    </div>

                    <div class="course-metrics-enhanced">
                        <div class="metric-item">
                            <span class="metric-label">Duration:</span>
                            <span class="metric-value">${course.duration}</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-label">Cost:</span>
                            <span class="metric-value">${course.cost}</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-label">Difficulty:</span>
                            <span class="metric-value difficulty-${course.difficulty.toLowerCase().replace(/\s+/g, '-')}">${course.difficulty}</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-label">Completion:</span>
                            <span class="metric-value">${course.completionRate}%</span>
                        </div>
                    </div>

                    <div class="course-relevance-enhanced">
                        <div class="relevance-header">
                            <span class="relevance-label">Relevance dla Andrii:</span>
                            <span class="relevance-value">${course.relevance}%</span>
                        </div>
                        <div class="relevance-bar">
                            <div class="relevance-fill" style="width: ${course.relevance}%"></div>
                        </div>
                    </div>

                    <div class="course-roi">
                        <div class="roi-item">
                            <i class="fas fa-chart-line"></i>
                            <div>
                                <div class="roi-label">Salary Impact:</div>
                                <div class="roi-value">${course.salaryIncrease}</div>
                            </div>
                        </div>
                        <div class="roi-item">
                            <i class="fas fa-clock"></i>
                            <div>
                                <div class="roi-label">ROI Timeline:</div>
                                <div class="roi-value">${course.roiMonths} months</div>
                            </div>
                        </div>
                    </div>

                    <div class="course-skills-enhanced">
                        <h4>Key Skills:</h4>
                        <div class="skills-container">
                            ${course.skills.map(skill => `<span class="skill-tag-enhanced">${skill}</span>`).join('')}
                        </div>
                    </div>

                    <div class="manufacturing-relevance-enhanced">
                        <h4><i class="fas fa-industry"></i> Manufacturing Application:</h4>
                        <p>${course.manufacturingRelevance}</p>
                    </div>

                    <div class="course-actions-enhanced">
                        <button class="course-btn-enhanced primary" onclick="enrollInCourseEnhanced('${course.id}')">
                            <i class="fas fa-play"></i>
                            Start Course
                        </button>
                        <button class="course-btn-enhanced secondary" onclick="viewCourseDetailsEnhanced('${course.id}')">
                            <i class="fas fa-info-circle"></i>
                            Details
                        </button>
                        <button class="course-btn-enhanced tertiary" onclick="addToLearningPath('${course.id}')">
                            <i class="fas fa-plus"></i>
                            Add to Path
                        </button>
                    </div>
                </div>
            `).join('')}
        </div>

        <div class="trends-highlight">
            <h3>🔥 AI Trends 2025 - Manufacturing Focus</h3>
            <div class="trends-grid">
                ${courseraCoursesData.emergingTrends2025.map(trend => `
                    <div class="trend-item">
                        <h4>${trend.trend}</h4>
                        <div class="trend-growth">+${trend.growth}% growth</div>
                        <div class="trend-relevance">${trend.relevanceToAndrii}% relevance dla Ciebie</div>
                    </div>
                `).join('')}
            </div>
        </div>

        <div class="platform-comparison">
            <h3>📊 Platform Comparison 2025</h3>
            <div class="comparison-grid">
                ${Object.entries(courseraCoursesData.platformComparison).map(([platform, data]) => `
                    <div class="platform-card">
                        <h4>${platform.charAt(0).toUpperCase() + platform.slice(1)}</h4>
                        <div class="platform-rating">
                            ${Array(Math.floor(data.rating)).fill('<i class="fas fa-star"></i>').join('')}
                            <span>${data.rating}/5</span>
                        </div>
                        <div class="platform-courses">${data.manufacturingCourses} manufacturing courses</div>
                        <div class="platform-best-for"><strong>Best for:</strong> ${data.bestFor}</div>
                        <div class="platform-strengths">
                            <ul>
                                ${data.strengths.map(strength => `<li>${strength}</li>`).join('')}
                            </ul>
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
}

// Initialize Certifications Section
function initializeCertifications() {
    console.log('🏆 Initializing Certifications section...');
    populateCertificationsGrid();
}

function populateCertificationsGrid() {
    const certificationsGrid = document.getElementById('certificationsGrid');
    if (!certificationsGrid) return;

    const premiumCertifications = [
        {
            id: 'aws-ml-specialty',
            title: 'AWS Machine Learning Specialty',
            provider: 'Amazon Web Services',
            cost: '€300 exam + prep courses',
            difficulty: 'Advanced',
            duration: '3-4 months prep',
            relevance: 96,
            salaryImpact: '+€20k-35k',
            roiMonths: 6,
            skills: ['AWS SageMaker', 'MLOps', 'Model Deployment', 'Cloud ML'],
            manufacturingRelevance: 'Perfect dla cloud-based manufacturing analytics i predictive maintenance',
            examFormat: '65 questions, 180 minutes',
            passingScore: '750/1000',
            trending: true
        },
        {
            id: 'google-cloud-ml',
            title: 'Google Cloud Professional ML Engineer',
            provider: 'Google Cloud',
            cost: '€200 exam + prep',
            difficulty: 'Advanced',
            duration: '2-3 months prep',
            relevance: 94,
            salaryImpact: '+€18k-30k',
            roiMonths: 5,
            skills: ['TensorFlow', 'Vertex AI', 'BigQuery ML', 'AutoML'],
            manufacturingRelevance: 'Excellent dla computer vision w quality control',
            examFormat: '50-60 questions, 120 minutes',
            passingScore: 'Pass/Fail',
            trending: true
        },
        {
            id: 'microsoft-azure-ai',
            title: 'Microsoft Azure AI Engineer Associate',
            provider: 'Microsoft',
            cost: '€165 exam',
            difficulty: 'Intermediate',
            duration: '2 months prep',
            relevance: 89,
            salaryImpact: '+€15k-25k',
            roiMonths: 4,
            skills: ['Azure ML', 'Cognitive Services', 'Bot Framework', 'Computer Vision'],
            manufacturingRelevance: 'Great dla enterprise manufacturing solutions',
            examFormat: '40-60 questions, 150 minutes',
            passingScore: '700/1000',
            trending: false
        },
        {
            id: 'nvidia-dli',
            title: 'NVIDIA Deep Learning Institute Certification',
            provider: 'NVIDIA',
            cost: '€90 per course',
            difficulty: 'Intermediate to Advanced',
            duration: '1-2 months',
            relevance: 92,
            salaryImpact: '+€12k-22k',
            roiMonths: 3,
            skills: ['CUDA', 'TensorRT', 'Edge AI', 'Computer Vision'],
            manufacturingRelevance: 'Perfect dla real-time manufacturing AI applications',
            examFormat: 'Hands-on assessment',
            passingScore: '80%',
            new: true
        },
        {
            id: 'databricks-ml',
            title: 'Databricks Certified ML Associate',
            provider: 'Databricks',
            cost: '€200 exam',
            difficulty: 'Intermediate',
            duration: '2 months prep',
            relevance: 87,
            salaryImpact: '+€14k-24k',
            roiMonths: 5,
            skills: ['MLflow', 'Apache Spark', 'Delta Lake', 'Feature Store'],
            manufacturingRelevance: 'Excellent dla big data analytics w manufacturing',
            examFormat: '45 questions, 90 minutes',
            passingScore: '70%',
            new: true
        }
    ];

    certificationsGrid.innerHTML = `
        <div class="certifications-overview">
            <h2>🏆 Premium Certifications dla Andrii</h2>
            <p>5 najlepszych certyfikacji AI/ML z highest ROI dla manufacturing professionals</p>
        </div>

        <div class="certifications-grid">
            ${premiumCertifications.map(cert => `
                <div class="certification-card ${cert.trending ? 'trending' : ''} ${cert.new ? 'new' : ''}">
                    ${cert.trending ? '<div class="trending-badge">🔥 TRENDING</div>' : ''}
                    ${cert.new ? '<div class="new-badge">✨ NEW 2025</div>' : ''}

                    <div class="cert-header">
                        <div class="cert-icon">
                            <i class="fas fa-certificate"></i>
                        </div>
                        <div class="cert-info">
                            <h3>${cert.title}</h3>
                            <p class="cert-provider">${cert.provider}</p>
                        </div>
                        <div class="cert-relevance">
                            <span class="relevance-score">${cert.relevance}%</span>
                            <span class="relevance-label">Relevance</span>
                        </div>
                    </div>

                    <div class="cert-metrics">
                        <div class="metric">
                            <span class="metric-label">Cost:</span>
                            <span class="metric-value">${cert.cost}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Prep Time:</span>
                            <span class="metric-value">${cert.duration}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Difficulty:</span>
                            <span class="metric-value">${cert.difficulty}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">ROI:</span>
                            <span class="metric-value positive">${cert.salaryImpact}</span>
                        </div>
                    </div>

                    <div class="cert-exam-details">
                        <h4>Exam Details:</h4>
                        <div class="exam-info">
                            <div class="exam-detail">
                                <span class="detail-label">Format:</span>
                                <span class="detail-value">${cert.examFormat}</span>
                            </div>
                            <div class="exam-detail">
                                <span class="detail-label">Passing Score:</span>
                                <span class="detail-value">${cert.passingScore}</span>
                            </div>
                        </div>
                    </div>

                    <div class="cert-skills">
                        <h4>Key Technologies:</h4>
                        <div class="skills-tags">
                            ${cert.skills.map(skill => `<span class="skill-tag">${skill}</span>`).join('')}
                        </div>
                    </div>

                    <div class="manufacturing-relevance">
                        <h4><i class="fas fa-industry"></i> Manufacturing Value:</h4>
                        <p>${cert.manufacturingRelevance}</p>
                    </div>

                    <div class="cert-actions">
                        <button class="btn-primary" onclick="startCertificationPrep('${cert.id}')">
                            <i class="fas fa-play"></i>
                            Start Prep
                        </button>
                        <button class="btn-secondary" onclick="viewCertificationDetails('${cert.id}')">
                            <i class="fas fa-info-circle"></i>
                            Details
                        </button>
                        <button class="btn-tertiary" onclick="addCertificationToGoals('${cert.id}')">
                            <i class="fas fa-target"></i>
                            Add to Goals
                        </button>
                    </div>
                </div>
            `).join('')}
        </div>

        <div class="certification-strategy">
            <h3>🎯 Certification Strategy dla Andrii</h3>
            <div class="strategy-grid">
                <div class="strategy-card immediate">
                    <h4>🚀 Immediate Priority (Next 3 months)</h4>
                    <ul>
                        <li><strong>AWS ML Specialty</strong> - Highest ROI (€20k-35k)</li>
                        <li>Perfect timing z Twoim Python experience</li>
                        <li>Cloud skills essential dla manufacturing 4.0</li>
                        <li>Strong demand w automotive industry</li>
                    </ul>
                </div>
                <div class="strategy-card medium">
                    <h4>📈 Medium-term (3-6 months)</h4>
                    <ul>
                        <li><strong>NVIDIA DLI</strong> - Edge AI specialization</li>
                        <li>Perfect dla real-time quality control</li>
                        <li>Complements AWS cloud knowledge</li>
                        <li>Growing demand w smart manufacturing</li>
                    </ul>
                </div>
                <div class="strategy-card longterm">
                    <h4>🎯 Long-term (6-12 months)</h4>
                    <ul>
                        <li><strong>Google Cloud ML Engineer</strong> - Computer vision focus</li>
                        <li>Multi-cloud expertise valuable</li>
                        <li>TensorFlow perfect dla manufacturing AI</li>
                        <li>Leadership positioning w organization</li>
                    </ul>
                </div>
            </div>
        </div>
    `;
}

// Certification action functions
function startCertificationPrep(certId) {
    addARIAMessage(`Excellent choice! Starting certification prep dla ${certId}. Based on your manufacturing background, I'll create a personalized study plan that connects cloud ML concepts to your SMT/CBA experience. Ready to begin?`);
    showQuickReplies([
        "Create study plan",
        "Find practice exams",
        "Join study group",
        "Schedule exam"
    ]);
    showNotification(`Started certification prep: ${certId}`, 'success');
}

function viewCertificationDetails(certId) {
    addARIAMessage(`Here are detailed insights dla ${certId} certification. This aligns perfectly with your career transformation goals. The manufacturing applications are particularly relevant to your Aptiv experience. Want me to break down the study approach?`);
    showQuickReplies([
        "Study roadmap",
        "Prerequisites check",
        "Cost breakdown",
        "Success tips"
    ]);
}

function addCertificationToGoals(certId) {
    userProgress.currentGoals.push(`Earn ${certId} certification`);
    saveUserProgress();
    addARIAMessage(`Added ${certId} to your career goals! This certification will significantly boost your AI credentials. I'll help you track progress and stay motivated throughout the journey.`);
    showQuickReplies([
        "Set timeline",
        "Track progress",
        "Find resources",
        "Connect with others"
    ]);
    showNotification(`Added to goals: ${certId}`, 'success');
}

// Export certification functions
window.startCertificationPrep = startCertificationPrep;
window.viewCertificationDetails = viewCertificationDetails;
window.addCertificationToGoals = addCertificationToGoals;

// Initialize Portfolio Section
function initializePortfolio() {
    console.log('💼 Initializing Portfolio section...');
    populatePortfolioGrid();
}

function populatePortfolioGrid() {
    const portfolioGrid = document.getElementById('portfolioGrid');
    if (!portfolioGrid) return;

    const portfolioProjects = [
        {
            id: 'smt-quality-vision',
            title: 'SMT Quality Control Computer Vision',
            description: 'AI-powered defect detection system dla SMT assembly lines using OpenCV and TensorFlow',
            status: 'recommended',
            difficulty: 'Intermediate',
            timeToComplete: '4-6 weeks',
            technologies: ['Python', 'OpenCV', 'TensorFlow', 'Keras', 'NumPy'],
            manufacturingRelevance: 'Direct application of your SMT/CBA expertise with cutting-edge AI',
            businessImpact: 'Reduce defect detection time by 75%, improve accuracy to 99.2%',
            githubStars: 0,
            priority: 'High',
            manusScore: 98
        },
        {
            id: 'pvd-predictive-maintenance',
            title: 'PVD Coating Process Predictive Maintenance',
            description: 'ML model predicting equipment failures w PVD coating systems using sensor data',
            status: 'in-progress',
            difficulty: 'Advanced',
            timeToComplete: '6-8 weeks',
            technologies: ['Python', 'Scikit-learn', 'Pandas', 'IoT Sensors', 'Time Series'],
            manufacturingRelevance: 'Leverages your PVD coating expertise for predictive analytics',
            businessImpact: 'Reduce unplanned downtime by 60%, save €200k annually',
            githubStars: 0,
            priority: 'High',
            manusScore: 96
        },
        {
            id: 'materials-property-prediction',
            title: 'Materials Property Prediction using AI',
            description: 'Deep learning model predicting material properties based on composition and structure',
            status: 'planned',
            difficulty: 'Advanced',
            timeToComplete: '8-10 weeks',
            technologies: ['Python', 'PyTorch', 'Materials Informatics', 'Graph Neural Networks'],
            manufacturingRelevance: 'Perfect match with your PhD in Materials Engineering',
            businessImpact: 'Accelerate new material discovery by 40%, reduce R&D costs',
            githubStars: 0,
            priority: 'Medium',
            manusScore: 95
        },
        {
            id: 'manufacturing-dashboard',
            title: 'Real-time Manufacturing Analytics Dashboard',
            description: 'Interactive dashboard dla real-time monitoring manufacturing KPIs using Power BI and Python',
            status: 'completed',
            difficulty: 'Beginner',
            timeToComplete: '2-3 weeks',
            technologies: ['Power BI', 'Python', 'SQL', 'REST APIs', 'DAX'],
            manufacturingRelevance: 'Immediate application w current Aptiv role',
            businessImpact: 'Improve decision-making speed by 50%, real-time visibility',
            githubStars: 12,
            priority: 'Medium',
            manusScore: 89
        },
        {
            id: 'automotive-ai-chatbot',
            title: 'Automotive Manufacturing AI Assistant',
            description: 'Conversational AI assistant dla manufacturing processes using LLMs and RAG',
            status: 'idea',
            difficulty: 'Intermediate',
            timeToComplete: '5-7 weeks',
            technologies: ['Python', 'LangChain', 'OpenAI API', 'Vector DB', 'Streamlit'],
            manufacturingRelevance: 'Combines AI with automotive manufacturing knowledge',
            businessImpact: 'Reduce training time by 30%, improve knowledge sharing',
            githubStars: 0,
            priority: 'Low',
            manusScore: 87
        }
    ];

    portfolioGrid.innerHTML = `
        <div class="portfolio-overview">
            <h2>💼 AI Portfolio dla Andrii</h2>
            <p>5 strategicznych projektów łączących manufacturing expertise z AI skills</p>
        </div>

        <div class="portfolio-stats">
            <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-project-diagram"></i></div>
                <div class="stat-info">
                    <div class="stat-number">5</div>
                    <div class="stat-label">Total Projects</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-check-circle"></i></div>
                <div class="stat-info">
                    <div class="stat-number">1</div>
                    <div class="stat-label">Completed</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-star"></i></div>
                <div class="stat-info">
                    <div class="stat-number">12</div>
                    <div class="stat-label">GitHub Stars</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-brain"></i></div>
                <div class="stat-info">
                    <div class="stat-number">93</div>
                    <div class="stat-label">Avg Manus Score</div>
                </div>
            </div>
        </div>

        <div class="portfolio-projects">
            ${portfolioProjects.map(project => `
                <div class="project-card ${project.status}">
                    <div class="project-header">
                        <div class="project-status-badge ${project.status}">
                            ${project.status === 'completed' ? '✅ Completed' :
                              project.status === 'in-progress' ? '🚧 In Progress' :
                              project.status === 'recommended' ? '⭐ Recommended' :
                              project.status === 'planned' ? '📋 Planned' : '💡 Idea'}
                        </div>
                        <div class="project-priority priority-${project.priority.toLowerCase()}">
                            ${project.priority} Priority
                        </div>
                    </div>

                    <div class="project-content">
                        <h3>${project.title}</h3>
                        <p class="project-description">${project.description}</p>

                        <div class="project-metrics">
                            <div class="metric">
                                <span class="metric-label">Difficulty:</span>
                                <span class="metric-value">${project.difficulty}</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Timeline:</span>
                                <span class="metric-value">${project.timeToComplete}</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Manus Score:</span>
                                <span class="metric-value">${project.manusScore}%</span>
                            </div>
                        </div>

                        <div class="project-technologies">
                            <h4>Technologies:</h4>
                            <div class="tech-tags">
                                ${project.technologies.map(tech => `<span class="tech-tag">${tech}</span>`).join('')}
                            </div>
                        </div>

                        <div class="manufacturing-connection">
                            <h4><i class="fas fa-industry"></i> Manufacturing Connection:</h4>
                            <p>${project.manufacturingRelevance}</p>
                        </div>

                        <div class="business-impact">
                            <h4><i class="fas fa-chart-line"></i> Business Impact:</h4>
                            <p>${project.businessImpact}</p>
                        </div>

                        <div class="project-actions">
                            ${project.status === 'completed' ?
                                `<button class="btn-primary" onclick="viewProject('${project.id}')">
                                    <i class="fas fa-eye"></i> View Project
                                </button>
                                <button class="btn-secondary" onclick="shareProject('${project.id}')">
                                    <i class="fas fa-share"></i> Share
                                </button>` :
                                project.status === 'in-progress' ?
                                `<button class="btn-primary" onclick="continueProject('${project.id}')">
                                    <i class="fas fa-play"></i> Continue
                                </button>
                                <button class="btn-secondary" onclick="viewProgress('${project.id}')">
                                    <i class="fas fa-chart-bar"></i> Progress
                                </button>` :
                                `<button class="btn-primary" onclick="startProject('${project.id}')">
                                    <i class="fas fa-rocket"></i> Start Project
                                </button>
                                <button class="btn-secondary" onclick="planProject('${project.id}')">
                                    <i class="fas fa-clipboard-list"></i> Plan
                                </button>`
                            }
                            <button class="btn-tertiary" onclick="getProjectHelp('${project.id}')">
                                <i class="fas fa-question-circle"></i> Get Help
                            </button>
                        </div>
                    </div>
                </div>
            `).join('')}
        </div>

        <div class="portfolio-strategy">
            <h3>🎯 Portfolio Strategy dla Andrii</h3>
            <div class="strategy-recommendations">
                <div class="recommendation-card">
                    <h4>🚀 Next 30 Days</h4>
                    <ul>
                        <li><strong>Start SMT Quality Vision project</strong> - highest Manus score (98%)</li>
                        <li>Perfect combination of your SMT expertise + AI learning</li>
                        <li>Immediate application potential w Aptiv</li>
                        <li>Strong portfolio piece dla job interviews</li>
                    </ul>
                </div>
                <div class="recommendation-card">
                    <h4>📈 Next 60 Days</h4>
                    <ul>
                        <li><strong>Continue PVD Predictive Maintenance</strong></li>
                        <li>Leverage your unique PVD coating knowledge</li>
                        <li>High business impact (€200k savings potential)</li>
                        <li>Advanced ML techniques showcase</li>
                    </ul>
                </div>
                <div class="recommendation-card">
                    <h4>🎯 Next 90 Days</h4>
                    <ul>
                        <li><strong>Plan Materials Property Prediction</strong></li>
                        <li>PhD-level project showcasing deep expertise</li>
                        <li>Cutting-edge Materials Informatics field</li>
                        <li>Research publication potential</li>
                    </ul>
                </div>
            </div>
        </div>
    `;
}

// Portfolio action functions
function startProject(projectId) {
    addARIAMessage(`Excellent choice! Starting ${projectId} project. Based on your manufacturing background, I'll guide you through each step. This project will perfectly showcase your unique combination of domain expertise and AI skills. Ready to begin?`);
    showQuickReplies([
        "Create project plan",
        "Set up environment",
        "Find resources",
        "Join project group"
    ]);
    showNotification(`Started project: ${projectId}`, 'success');
}

function continueProject(projectId) {
    addARIAMessage(`Great to see you continuing with ${projectId}! Let me check your current progress and suggest the next steps. Your manufacturing insights are really adding value to this AI project.`);
    showQuickReplies([
        "Show progress",
        "Next milestone",
        "Get feedback",
        "Troubleshoot issues"
    ]);
}

function viewProject(projectId) {
    addARIAMessage(`Here's your completed ${projectId} project! This is a fantastic example of applying AI to manufacturing. The combination of your domain knowledge and technical skills really shows. Want to discuss how to leverage this for your career?`);
    showQuickReplies([
        "Add to LinkedIn",
        "Create case study",
        "Present to team",
        "Improve project"
    ]);
}

function planProject(projectId) {
    addARIAMessage(`Let's plan ${projectId} project strategically! I'll help you break it down into manageable milestones that align with your learning goals and showcase your manufacturing expertise.`);
    showQuickReplies([
        "Create timeline",
        "Define milestones",
        "Resource planning",
        "Success metrics"
    ]);
}

function getProjectHelp(projectId) {
    addARIAMessage(`I'm here to help with ${projectId}! Whether you need technical guidance, manufacturing domain insights, or career strategy advice, let's work through it together.`);
    showQuickReplies([
        "Technical help",
        "Domain guidance",
        "Career strategy",
        "Find mentors"
    ]);
}

// Export portfolio functions
window.startProject = startProject;
window.continueProject = continueProject;
window.viewProject = viewProject;
window.planProject = planProject;
window.getProjectHelp = getProjectHelp;

// Initialize Networking Section
function initializeNetworking() {
    console.log('🤝 Initializing Networking section...');
    populateNetworkingGrid();
}

function populateNetworkingGrid() {
    const networkingGrid = document.getElementById('networkingGrid');
    if (!networkingGrid) return;

    const networkingEvents = [
        {
            id: 'trojmiasto-ai-meetup',
            title: 'Trójmiasto AI Meetup',
            date: '25 stycznia 2025',
            time: '18:00 - 21:00',
            location: 'Gdańsk Tech Park',
            type: 'Local Meetup',
            relevance: 94,
            attendees: 85,
            cost: 'Free',
            focus: 'AI in Manufacturing',
            speakers: ['Dr. Anna Kowalska (Gdańsk Tech)', 'Marcin Nowak (Intel)'],
            topics: ['Computer Vision w przemyśle', 'Edge AI applications', 'Networking session'],
            manusRecommendation: 'PRIORITY - Perfect dla Twojej lokalizacji i manufacturing focus',
            registrationStatus: 'open',
            priority: 'high'
        },
        {
            id: 'global-ai-bootcamp-krakow',
            title: 'Global AI Bootcamp Kraków',
            date: '7 marca 2025',
            time: '09:00 - 17:00',
            location: 'Kraków Technology Park',
            type: 'Conference',
            relevance: 96,
            attendees: 300,
            cost: '€45 early bird',
            focus: 'AI/ML Fundamentals',
            speakers: ['Microsoft MVP Team', 'Google AI Experts'],
            topics: ['Azure ML Workshop', 'TensorFlow hands-on', 'Career transitions'],
            manusRecommendation: 'MUST ATTEND - Perfect dla career transition',
            registrationStatus: 'early-bird',
            priority: 'high'
        },
        {
            id: 'ai-manufacturing-conference',
            title: 'AI in Manufacturing Conference',
            date: '20 maja 2025',
            time: '08:00 - 18:00',
            location: 'Warsaw Expo Center',
            type: 'Industry Conference',
            relevance: 98,
            attendees: 500,
            cost: '€120 professional',
            focus: 'Manufacturing AI',
            speakers: ['Siemens AI Team', 'Bosch Industry 4.0', 'Aptiv Innovation'],
            topics: ['Predictive Maintenance', 'Quality Control AI', 'Smart Factory'],
            manusRecommendation: 'PERFECT FIT - Your exact industry + AI combination',
            registrationStatus: 'open',
            priority: 'critical'
        },
        {
            id: 'data-science-summit-warsaw',
            title: 'Data Science Summit Warsaw',
            date: '15 kwietnia 2025',
            time: '09:00 - 18:00',
            location: 'Palace of Culture, Warsaw',
            type: 'Summit',
            relevance: 92,
            attendees: 800,
            cost: '€85 standard',
            focus: 'Data Science & Analytics',
            speakers: ['Netflix Data Team', 'Spotify ML Engineers'],
            topics: ['MLOps best practices', 'Real-time analytics', 'Career panel'],
            manusRecommendation: 'EXCELLENT - Strong technical content + networking',
            registrationStatus: 'open',
            priority: 'high'
        },
        {
            id: 'pydata-warsaw',
            title: 'PyData Warsaw',
            date: '28 lutego 2025',
            time: '18:00 - 22:00',
            location: 'Google Campus Warsaw',
            type: 'Technical Meetup',
            relevance: 89,
            attendees: 120,
            cost: 'Free',
            focus: 'Python for Data Science',
            speakers: ['Local Python experts', 'Data scientists'],
            topics: ['Pandas optimization', 'Scikit-learn tips', 'Open source projects'],
            manusRecommendation: 'GOOD - Technical skills + Python community',
            registrationStatus: 'open',
            priority: 'medium'
        }
    ];

    networkingGrid.innerHTML = `
        <div class="networking-overview">
            <h2>🤝 Networking Events dla Andrii</h2>
            <p>12 strategicznych wydarzeń AI w Polsce - spersonalizowane dla Twojej transformacji kariery</p>
        </div>

        <div class="networking-stats">
            <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-calendar"></i></div>
                <div class="stat-info">
                    <div class="stat-number">12</div>
                    <div class="stat-label">Events This Year</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-users"></i></div>
                <div class="stat-info">
                    <div class="stat-number">2,100+</div>
                    <div class="stat-label">Total Attendees</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-star"></i></div>
                <div class="stat-info">
                    <div class="stat-number">93%</div>
                    <div class="stat-label">Avg Relevance</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-map-marker-alt"></i></div>
                <div class="stat-info">
                    <div class="stat-number">5</div>
                    <div class="stat-label">Cities</div>
                </div>
            </div>
        </div>

        <div class="events-grid">
            ${networkingEvents.map(event => `
                <div class="event-card priority-${event.priority}">
                    <div class="event-header">
                        <div class="event-priority priority-${event.priority}">
                            ${event.priority === 'critical' ? '🔥 CRITICAL' :
                              event.priority === 'high' ? '⭐ HIGH' : '📋 MEDIUM'} Priority
                        </div>
                        <div class="event-relevance">
                            <span class="relevance-score">${event.relevance}%</span>
                            <span class="relevance-label">Relevance</span>
                        </div>
                    </div>

                    <div class="event-content">
                        <h3>${event.title}</h3>

                        <div class="event-details">
                            <div class="detail-item">
                                <i class="fas fa-calendar"></i>
                                <span>${event.date}</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-clock"></i>
                                <span>${event.time}</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>${event.location}</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-users"></i>
                                <span>${event.attendees} attendees</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-euro-sign"></i>
                                <span>${event.cost}</span>
                            </div>
                        </div>

                        <div class="event-focus">
                            <h4><i class="fas fa-bullseye"></i> Focus Area:</h4>
                            <span class="focus-tag">${event.focus}</span>
                        </div>

                        <div class="event-speakers">
                            <h4><i class="fas fa-microphone"></i> Key Speakers:</h4>
                            <ul>
                                ${event.speakers.map(speaker => `<li>${speaker}</li>`).join('')}
                            </ul>
                        </div>

                        <div class="event-topics">
                            <h4><i class="fas fa-list"></i> Main Topics:</h4>
                            <div class="topics-tags">
                                ${event.topics.map(topic => `<span class="topic-tag">${topic}</span>`).join('')}
                            </div>
                        </div>

                        <div class="manus-recommendation">
                            <h4><i class="fas fa-brain"></i> Manus AI Recommendation:</h4>
                            <p>${event.manusRecommendation}</p>
                        </div>

                        <div class="event-actions">
                            <button class="btn-primary" onclick="registerForEvent('${event.id}')">
                                <i class="fas fa-ticket-alt"></i>
                                Register Now
                            </button>
                            <button class="btn-secondary" onclick="addToCalendar('${event.id}')">
                                <i class="fas fa-calendar-plus"></i>
                                Add to Calendar
                            </button>
                            <button class="btn-tertiary" onclick="getEventDetails('${event.id}')">
                                <i class="fas fa-info-circle"></i>
                                More Details
                            </button>
                        </div>
                    </div>
                </div>
            `).join('')}
        </div>

        <div class="networking-strategy">
            <h3>🎯 Networking Strategy dla Andrii</h3>
            <div class="strategy-timeline">
                <div class="timeline-item">
                    <div class="timeline-date">Styczeń 2025</div>
                    <div class="timeline-content">
                        <h4>🚀 Start Local</h4>
                        <p><strong>Trójmiasto AI Meetup</strong> - Perfect introduction do local AI community. Close to home, manufacturing focus, manageable size.</p>
                        <div class="action-items">
                            <span class="action-tag">Prepare elevator pitch</span>
                            <span class="action-tag">Bring business cards</span>
                            <span class="action-tag">Follow up on LinkedIn</span>
                        </div>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-date">Marzec 2025</div>
                    <div class="timeline-content">
                        <h4>📈 Scale Up</h4>
                        <p><strong>Global AI Bootcamp</strong> - Larger event, hands-on workshops, career transition focus. Perfect timing dla skill building.</p>
                        <div class="action-items">
                            <span class="action-tag">Attend workshops</span>
                            <span class="action-tag">Connect with career changers</span>
                            <span class="action-tag">Learn about opportunities</span>
                        </div>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-date">Maj 2025</div>
                    <div class="timeline-content">
                        <h4>🎯 Industry Focus</h4>
                        <p><strong>AI in Manufacturing Conference</strong> - Your perfect event! Industry experts, Aptiv presence, direct career relevance.</p>
                        <div class="action-items">
                            <span class="action-tag">Present your projects</span>
                            <span class="action-tag">Meet industry leaders</span>
                            <span class="action-tag">Explore job opportunities</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="networking-tips">
            <h3>💡 Networking Tips dla Manufacturing Professionals</h3>
            <div class="tips-grid">
                <div class="tip-card">
                    <h4>🎯 Your Unique Value</h4>
                    <p>Emphasize your rare combination: 15+ years manufacturing + PhD Materials + AI transition. This makes you incredibly valuable.</p>
                </div>
                <div class="tip-card">
                    <h4>🗣️ Elevator Pitch</h4>
                    <p>"I'm transforming 15 years of manufacturing expertise into AI solutions. Currently working on computer vision dla quality control."</p>
                </div>
                <div class="tip-card">
                    <h4>🤝 Connection Strategy</h4>
                    <p>Target: AI engineers interested in manufacturing, manufacturing professionals exploring AI, recruiters from automotive companies.</p>
                </div>
                <div class="tip-card">
                    <h4>📱 Follow-up</h4>
                    <p>Connect within 24 hours on LinkedIn. Reference specific conversation points. Share relevant manufacturing AI insights.</p>
                </div>
            </div>
        </div>
    `;
}

// Networking action functions
function registerForEvent(eventId) {
    addARIAMessage(`Excellent choice! Registering dla ${eventId}. This event aligns perfectly with your career transformation goals. I'll help you prepare to make the most of this networking opportunity. Ready to plan your approach?`);
    showQuickReplies([
        "Prepare elevator pitch",
        "Research attendees",
        "Set networking goals",
        "Plan follow-up strategy"
    ]);
    showNotification(`Registered for event: ${eventId}`, 'success');
}

function addToCalendar(eventId) {
    addARIAMessage(`Added ${eventId} to your calendar! I'll send you reminders and preparation tips as the date approaches. Want me to help you prepare for maximum networking impact?`);
    showQuickReplies([
        "Preparation checklist",
        "Networking goals",
        "Research speakers",
        "Connect with attendees"
    ]);
    showNotification(`Added to calendar: ${eventId}`, 'success');
}

function getEventDetails(eventId) {
    addARIAMessage(`Here are detailed insights dla ${eventId}. Based on your manufacturing background and AI transition goals, this event offers specific opportunities. Let me break down the strategic value for your career.`);
    showQuickReplies([
        "Strategic value",
        "Key contacts",
        "Preparation tips",
        "ROI analysis"
    ]);
}

// Export networking functions
window.registerForEvent = registerForEvent;
window.addToCalendar = addToCalendar;
window.getEventDetails = getEventDetails;

// Initialize Roadmap Section
function initializeRoadmap() {
    console.log('🗺️ Initializing Roadmap section...');
    populateRoadmapGrid();
}

function populateRoadmapGrid() {
    const roadmapGrid = document.getElementById('roadmapGrid');
    if (!roadmapGrid) return;

    roadmapGrid.innerHTML = `
        <div class="roadmap-overview">
            <h2>🗺️ Career Roadmap dla Andrii</h2>
            <p>12-miesięczny plan transformacji z Manufacturing Engineer do AI/Data Science Leader</p>
        </div>

        <div class="roadmap-timeline">
            <div class="timeline-phase phase-1">
                <div class="phase-header">
                    <h3>Phase 1: Foundation</h3>
                    <span class="phase-duration">Months 1-2</span>
                </div>
                <div class="phase-content">
                    <div class="phase-goals">
                        <h4>🎯 Goals:</h4>
                        <ul>
                            <li>Python proficiency dla data science</li>
                            <li>ML fundamentals understanding</li>
                            <li>First AI project completion</li>
                            <li>Local AI community engagement</li>
                        </ul>
                    </div>
                    <div class="phase-actions">
                        <h4>📋 Key Actions:</h4>
                        <div class="action-items">
                            <span class="action-tag completed">✅ Python Basics</span>
                            <span class="action-tag in-progress">🚧 ML Course</span>
                            <span class="action-tag pending">📋 First Project</span>
                            <span class="action-tag pending">📋 Trójmiasto AI</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="timeline-phase phase-2">
                <div class="phase-header">
                    <h3>Phase 2: Skill Building</h3>
                    <span class="phase-duration">Months 3-4</span>
                </div>
                <div class="phase-content">
                    <div class="phase-goals">
                        <h4>🎯 Goals:</h4>
                        <ul>
                            <li>Advanced analytics capabilities</li>
                            <li>Deep learning fundamentals</li>
                            <li>Manufacturing AI project</li>
                            <li>Professional network expansion</li>
                        </ul>
                    </div>
                    <div class="phase-actions">
                        <h4>📋 Key Actions:</h4>
                        <div class="action-items">
                            <span class="action-tag pending">📋 Advanced Analytics</span>
                            <span class="action-tag pending">📋 Deep Learning</span>
                            <span class="action-tag pending">📋 SMT Vision Project</span>
                            <span class="action-tag pending">📋 AI Bootcamp</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="timeline-phase phase-3">
                <div class="phase-header">
                    <h3>Phase 3: Specialization</h3>
                    <span class="phase-duration">Months 5-6</span>
                </div>
                <div class="phase-content">
                    <div class="phase-goals">
                        <h4>🎯 Goals:</h4>
                        <ul>
                            <li>AI in Manufacturing expertise</li>
                            <li>Cloud ML platform mastery</li>
                            <li>Industry recognition building</li>
                            <li>Portfolio showcase development</li>
                        </ul>
                    </div>
                    <div class="phase-actions">
                        <h4>📋 Key Actions:</h4>
                        <div class="action-items">
                            <span class="action-tag pending">📋 Manufacturing AI</span>
                            <span class="action-tag pending">📋 AWS ML Cert</span>
                            <span class="action-tag pending">📋 Conference Talk</span>
                            <span class="action-tag pending">📋 Portfolio Website</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="timeline-phase phase-4">
                <div class="phase-header">
                    <h3>Phase 4: Professional Development</h3>
                    <span class="phase-duration">Months 7-8</span>
                </div>
                <div class="phase-content">
                    <div class="phase-goals">
                        <h4>🎯 Goals:</h4>
                        <ul>
                            <li>Professional certifications</li>
                            <li>Job market preparation</li>
                            <li>Industry connections</li>
                            <li>Thought leadership</li>
                        </ul>
                    </div>
                    <div class="phase-actions">
                        <h4>📋 Key Actions:</h4>
                        <div class="action-items">
                            <span class="action-tag pending">📋 Certifications</span>
                            <span class="action-tag pending">📋 Job Applications</span>
                            <span class="action-tag pending">📋 Industry Events</span>
                            <span class="action-tag pending">📋 Content Creation</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="timeline-phase phase-5">
                <div class="phase-header">
                    <h3>Phase 5: Transition</h3>
                    <span class="phase-duration">Months 9-10</span>
                </div>
                <div class="phase-content">
                    <div class="phase-goals">
                        <h4>🎯 Goals:</h4>
                        <ul>
                            <li>Job offers evaluation</li>
                            <li>Successful role transition</li>
                            <li>New team integration</li>
                            <li>Impact demonstration</li>
                        </ul>
                    </div>
                    <div class="phase-actions">
                        <h4>📋 Key Actions:</h4>
                        <div class="action-items">
                            <span class="action-tag pending">📋 Job Offers</span>
                            <span class="action-tag pending">📋 Role Transition</span>
                            <span class="action-tag pending">📋 Team Integration</span>
                            <span class="action-tag pending">📋 Quick Wins</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="timeline-phase phase-6">
                <div class="phase-header">
                    <h3>Phase 6: Growth</h3>
                    <span class="phase-duration">Months 11-12</span>
                </div>
                <div class="phase-content">
                    <div class="phase-goals">
                        <h4>🎯 Goals:</h4>
                        <ul>
                            <li>Leadership responsibilities</li>
                            <li>Future planning</li>
                            <li>Mentoring others</li>
                            <li>Continuous learning</li>
                        </ul>
                    </div>
                    <div class="phase-actions">
                        <h4>📋 Key Actions:</h4>
                        <div class="action-items">
                            <span class="action-tag pending">📋 Leadership Role</span>
                            <span class="action-tag pending">📋 Future Strategy</span>
                            <span class="action-tag pending">📋 Mentoring</span>
                            <span class="action-tag pending">📋 Advanced Skills</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="roadmap-metrics">
            <h3>📊 Success Metrics</h3>
            <div class="metrics-grid">
                <div class="metric-card">
                    <h4>💰 Salary Growth</h4>
                    <div class="metric-value">€70k → €120k+</div>
                    <div class="metric-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 35%"></div>
                        </div>
                        <span>35% progress</span>
                    </div>
                </div>
                <div class="metric-card">
                    <h4>🎓 Skills Acquired</h4>
                    <div class="metric-value">8/15 Core Skills</div>
                    <div class="metric-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 53%"></div>
                        </div>
                        <span>53% complete</span>
                    </div>
                </div>
                <div class="metric-card">
                    <h4>🤝 Network Size</h4>
                    <div class="metric-value">45/200 Connections</div>
                    <div class="metric-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 23%"></div>
                        </div>
                        <span>23% of target</span>
                    </div>
                </div>
                <div class="metric-card">
                    <h4>💼 Portfolio Projects</h4>
                    <div class="metric-value">1/5 Completed</div>
                    <div class="metric-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 20%"></div>
                        </div>
                        <span>20% complete</span>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Initialize Dashboard Section
function initializeDashboard() {
    console.log('📊 Initializing Dashboard section...');
    // Dashboard is already initialized in the main HTML
    // This function can be used for dynamic updates
}

// ARIA Popup and AI Integration System
class ARIAAgent {
    constructor() {
        this.isPopupOpen = false;
        this.aiSettings = this.loadAISettings();
        this.conversationHistory = [];
        this.isConnected = false;
        this.setupEventListeners();
        this.initializeAI();
        this.setupQuickReplies();
    }

    loadAISettings() {
        const defaultSettings = {
            provider: 'grok',
            model: 'grok-beta',
            apiKey: '',
            apiEndpoint: '',
            temperature: 0.7,
            maxTokens: 2000,
            systemPrompt: `Jesteś ARIA, ekspertem AI w transformacji kariery dla manufacturing professionals.
Specjalizujesz się w pomaganiu inżynierom produkcji w przejściu do ról AI/Data Science.
Twoja wiedza obejmuje:
- Manufacturing processes (SMT, CBA, PVD coating)
- AI/ML technologies i applications
- Career development strategies
- Polish job market w automotive industry
- Manus AI logic dla personalized insights

Odpowiadaj w języku polskim, bądź profesjonalny ale przyjazny, i zawsze łącz manufacturing experience z AI opportunities.`,
            userProfile: 'andrii',
            responseStyle: 'professional',
            language: 'pl',
            enableEncryption: true,
            enableLogging: false
        };

        const saved = localStorage.getItem('aria-ai-settings');
        return saved ? { ...defaultSettings, ...JSON.parse(saved) } : defaultSettings;
    }

    saveAISettings() {
        localStorage.setItem('aria-ai-settings', JSON.stringify(this.aiSettings));
    }

    setupEventListeners() {
        // Floating button - toggle popup
        document.getElementById('ariaFloatingBtn').addEventListener('click', () => this.togglePopup());

        // Close button
        document.getElementById('ariaClose').addEventListener('click', () => this.closePopup());

        // Settings button
        document.getElementById('ariaSettings').addEventListener('click', () => this.openAISettings());

        // Settings modal controls
        document.getElementById('testConnection').addEventListener('click', () => this.testConnection());

        // Settings sliders
        document.getElementById('temperature').addEventListener('input', (e) => {
            document.getElementById('temperatureValue').textContent = e.target.value;
        });

        document.getElementById('maxTokens').addEventListener('input', (e) => {
            document.getElementById('maxTokensValue').textContent = e.target.value;
        });

        // Enhanced chat input with AI integration
        const chatInput = document.getElementById('chatInput');
        const sendButton = document.getElementById('sendButton');

        chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        sendButton.addEventListener('click', () => this.sendMessage());

        // Click outside popup to close
        document.addEventListener('click', (e) => {
            const popup = document.getElementById('ariaChatPopup');
            const floatingBtn = document.getElementById('ariaFloatingBtn');

            if (this.isPopupOpen &&
                !popup.contains(e.target) &&
                !floatingBtn.contains(e.target)) {
                this.closePopup();
            }
        });

        // Escape key to close popup
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isPopupOpen) {
                this.closePopup();
            }
        });
    }

    togglePopup() {
        if (this.isPopupOpen) {
            this.closePopup();
        } else {
            this.openPopup();
        }
    }

    openPopup() {
        const popup = document.getElementById('ariaChatPopup');
        popup.classList.remove('hidden');
        this.isPopupOpen = true;

        // Focus on chat input
        setTimeout(() => {
            const chatInput = document.getElementById('chatInput');
            if (chatInput) {
                chatInput.focus();
            }
        }, 300);

        // Hide notification if visible
        this.hideNotification();
    }

    closePopup() {
        const popup = document.getElementById('ariaChatPopup');
        popup.classList.add('hidden');
        this.isPopupOpen = false;
    }

    showNotification() {
        const notification = document.getElementById('floatingNotification');
        notification.classList.remove('hidden');
    }

    hideNotification() {
        const notification = document.getElementById('floatingNotification');
        notification.classList.add('hidden');
    }

    openAISettings() {
        const modal = document.getElementById('aiSettingsModal');
        modal.style.display = 'block';
        this.populateSettingsForm();
    }

    populateSettingsForm() {
        document.getElementById('aiProvider').value = this.aiSettings.provider;
        document.getElementById('aiModel').value = this.aiSettings.model;
        document.getElementById('apiKey').value = this.aiSettings.apiKey;
        document.getElementById('apiEndpoint').value = this.aiSettings.apiEndpoint;
        document.getElementById('temperature').value = this.aiSettings.temperature;
        document.getElementById('maxTokens').value = this.aiSettings.maxTokens;
        document.getElementById('systemPrompt').value = this.aiSettings.systemPrompt;
        document.getElementById('userProfile').value = this.aiSettings.userProfile;
        document.getElementById('responseStyle').value = this.aiSettings.responseStyle;
        document.getElementById('language').value = this.aiSettings.language;
        document.getElementById('enableEncryption').checked = this.aiSettings.enableEncryption;
        document.getElementById('enableLogging').checked = this.aiSettings.enableLogging;

        document.getElementById('temperatureValue').textContent = this.aiSettings.temperature;
        document.getElementById('maxTokensValue').textContent = this.aiSettings.maxTokens;
    }

    async testConnection() {
        const statusIndicator = document.querySelector('#connectionStatus .status-indicator');
        const testButton = document.getElementById('testConnection');

        statusIndicator.className = 'status-indicator connecting';
        statusIndicator.innerHTML = '<span class="status-dot"></span><span>Łączenie...</span>';
        testButton.disabled = true;

        try {
            const response = await this.callAI('Test connection - respond with "Connection successful"');

            if (response) {
                statusIndicator.className = 'status-indicator online';
                statusIndicator.innerHTML = '<span class="status-dot"></span><span>Połączono</span>';
                this.isConnected = true;
                showNotification('Połączenie z AI nawiązane pomyślnie!', 'success');
            } else {
                throw new Error('No response from AI');
            }
        } catch (error) {
            statusIndicator.className = 'status-indicator offline';
            statusIndicator.innerHTML = '<span class="status-dot"></span><span>Błąd połączenia</span>';
            this.isConnected = false;
            showNotification('Błąd połączenia z AI: ' + error.message, 'error');
        }

        testButton.disabled = false;
    }

    async callAI(message, context = '') {
        if (!this.aiSettings.apiKey) {
            throw new Error('Brak klucza API. Skonfiguruj ustawienia AI.');
        }

        const fullPrompt = `${this.aiSettings.systemPrompt}\n\nContext: ${context}\n\nUser: ${message}`;

        try {
            let response;

            switch (this.aiSettings.provider) {
                case 'grok':
                    response = await this.callGrokAPI(fullPrompt);
                    break;
                case 'google':
                    response = await this.callGoogleAPI(fullPrompt);
                    break;
                case 'openai':
                    response = await this.callOpenAIAPI(fullPrompt);
                    break;
                default:
                    throw new Error('Nieobsługiwany dostawca AI');
            }

            // Add to conversation history
            this.conversationHistory.push({
                user: message,
                assistant: response,
                timestamp: new Date().toISOString(),
                context: context
            });

            // Save conversation if logging enabled
            if (this.aiSettings.enableLogging) {
                this.saveConversationHistory();
            }

            return response;

        } catch (error) {
            console.error('AI API Error:', error);
            throw error;
        }
    }

    async callGrokAPI(prompt) {
        const endpoint = this.aiSettings.apiEndpoint || 'https://api.x.ai/v1/chat/completions';

        const response = await fetch(endpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.aiSettings.apiKey}`
            },
            body: JSON.stringify({
                model: this.aiSettings.model,
                messages: [
                    { role: 'user', content: prompt }
                ],
                temperature: parseFloat(this.aiSettings.temperature),
                max_tokens: parseInt(this.aiSettings.maxTokens)
            })
        });

        if (!response.ok) {
            throw new Error(`Grok API error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        return data.choices[0].message.content;
    }

    async callGoogleAPI(prompt) {
        const endpoint = this.aiSettings.apiEndpoint || `https://generativelanguage.googleapis.com/v1beta/models/${this.aiSettings.model}:generateContent?key=${this.aiSettings.apiKey}`;

        const response = await fetch(endpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                contents: [{
                    parts: [{ text: prompt }]
                }],
                generationConfig: {
                    temperature: parseFloat(this.aiSettings.temperature),
                    maxOutputTokens: parseInt(this.aiSettings.maxTokens)
                }
            })
        });

        if (!response.ok) {
            throw new Error(`Google API error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        return data.candidates[0].content.parts[0].text;
    }

    async callOpenAIAPI(prompt) {
        const endpoint = this.aiSettings.apiEndpoint || 'https://api.openai.com/v1/chat/completions';

        const response = await fetch(endpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.aiSettings.apiKey}`
            },
            body: JSON.stringify({
                model: this.aiSettings.model,
                messages: [
                    { role: 'user', content: prompt }
                ],
                temperature: parseFloat(this.aiSettings.temperature),
                max_tokens: parseInt(this.aiSettings.maxTokens)
            })
        });

        if (!response.ok) {
            throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        return data.choices[0].message.content;
    }

    async sendMessage() {
        const chatInput = document.getElementById('chatInput');
        const message = chatInput.value.trim();

        if (!message) return;

        // Clear input
        chatInput.value = '';

        // Add user message to chat
        this.addMessageToChat(message, 'user');

        // Show typing indicator
        this.showTypingIndicator();

        try {
            // Get current context from the page
            const context = this.getCurrentContext();

            // Call AI
            const response = await this.callAI(message, context);

            // Remove typing indicator
            this.hideTypingIndicator();

            // Add AI response to chat
            this.addMessageToChat(response, 'assistant');

        } catch (error) {
            this.hideTypingIndicator();
            this.addMessageToChat(`Przepraszam, wystąpił błąd: ${error.message}. Sprawdź ustawienia AI.`, 'error');
        }
    }

    addMessageToChat(message, sender) {
        const chatMessages = document.getElementById('chatMessages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `chat-message ${sender}`;

        const timestamp = new Date().toLocaleTimeString('pl-PL', {
            hour: '2-digit',
            minute: '2-digit'
        });

        messageDiv.innerHTML = `
            <div class="message-content">
                <div class="message-text">${this.formatMessage(message)}</div>
                <div class="message-time">${timestamp}</div>
            </div>
        `;

        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;

        // Show notification if popup is closed and message is from assistant
        if (!this.isPopupOpen && sender === 'assistant') {
            this.showNotification();
        }
    }

    formatMessage(message) {
        // Convert markdown-like formatting to HTML
        return message
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/\n/g, '<br>');
    }

    showTypingIndicator() {
        const chatMessages = document.getElementById('chatMessages');
        const typingDiv = document.createElement('div');
        typingDiv.className = 'chat-message assistant typing';
        typingDiv.id = 'typingIndicator';
        typingDiv.innerHTML = `
            <div class="message-content">
                <div class="typing-animation">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        `;
        chatMessages.appendChild(typingDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    hideTypingIndicator() {
        const typingIndicator = document.getElementById('typingIndicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }

    getCurrentContext() {
        // Get current active section
        const activeSection = document.querySelector('.section.active');
        const sectionId = activeSection ? activeSection.id : 'dashboard';

        // Build context based on current section
        let context = `Current section: ${sectionId}\n`;
        context += `User profile: Andrii Zinchuk - Manufacturing Engineer transitioning to AI/Data Science\n`;
        context += `Background: 15+ years manufacturing, PhD Materials, SMT/CBA/PVD expertise\n`;
        context += `Location: Gdańsk, Poland\n`;
        context += `Current company: Aptiv\n`;

        return context;
    }

    saveConversationHistory() {
        if (this.aiSettings.enableEncryption) {
            // Simple encryption (in production, use proper encryption)
            const encrypted = btoa(JSON.stringify(this.conversationHistory));
            localStorage.setItem('aria-conversation-history', encrypted);
        } else {
            localStorage.setItem('aria-conversation-history', JSON.stringify(this.conversationHistory));
        }
    }

    setupQuickReplies() {
        const quickReplies = [
            "Jakie kursy Coursera polecasz?",
            "Pomóż z planem kariery",
            "Certyfikacje AI dla automotive",
            "Networking w AI",
            "Portfolio projects",
            "Salary expectations"
        ];

        const quickRepliesContainer = document.getElementById('quickReplies');
        quickRepliesContainer.innerHTML = '';

        quickReplies.forEach(reply => {
            const button = document.createElement('button');
            button.className = 'quick-reply-btn';
            button.textContent = reply;
            button.addEventListener('click', () => {
                document.getElementById('chatInput').value = reply;
                this.sendMessage();
            });
            quickRepliesContainer.appendChild(button);
        });
    }

    initializeAI() {
        // Initialize with welcome message if no API key
        if (!this.aiSettings.apiKey) {
            setTimeout(() => {
                this.addMessageToChat(
                    'Cześć Andrii! 👋 Jestem ARIA, Twój AI Coach. Aby w pełni wykorzystać moje możliwości, skonfiguruj ustawienia AI (kliknij ikonę ⚙️). Mogę pomóc Ci z transformacją kariery z Manufacturing do AI/Data Science!',
                    'assistant'
                );
                this.showNotification();
            }, 1000);
        }
    }
}

// AI Settings Modal Functions
function closeAISettingsModal() {
    document.getElementById('aiSettingsModal').style.display = 'none';
}

function saveAISettings() {
    const settings = {
        provider: document.getElementById('aiProvider').value,
        model: document.getElementById('aiModel').value,
        apiKey: document.getElementById('apiKey').value,
        apiEndpoint: document.getElementById('apiEndpoint').value,
        temperature: parseFloat(document.getElementById('temperature').value),
        maxTokens: parseInt(document.getElementById('maxTokens').value),
        systemPrompt: document.getElementById('systemPrompt').value,
        userProfile: document.getElementById('userProfile').value,
        responseStyle: document.getElementById('responseStyle').value,
        language: document.getElementById('language').value,
        enableEncryption: document.getElementById('enableEncryption').checked,
        enableLogging: document.getElementById('enableLogging').checked
    };

    ariaAgent.aiSettings = settings;
    ariaAgent.saveAISettings();

    showNotification('Ustawienia AI zostały zapisane!', 'success');
    closeAISettingsModal();
}

function resetAISettings() {
    if (confirm('Czy na pewno chcesz zresetować wszystkie ustawienia AI?')) {
        localStorage.removeItem('aria-ai-settings');
        ariaAgent.aiSettings = ariaAgent.loadAISettings();
        ariaAgent.populateSettingsForm();
        showNotification('Ustawienia AI zostały zresetowane!', 'info');
    }
}

// Initialize ARIA Agent
let ariaAgent;

// Enhanced addARIAMessage function to work with AI
function addARIAMessage(message) {
    if (ariaAgent) {
        ariaAgent.addMessageToChat(message, 'assistant');
    } else {
        // Fallback to original function
        const chatMessages = document.getElementById('chatMessages');
        const messageDiv = document.createElement('div');
        messageDiv.className = 'chat-message assistant';
        messageDiv.innerHTML = `
            <div class="message-content">
                <div class="message-text">${message}</div>
                <div class="message-time">${new Date().toLocaleTimeString('pl-PL', { hour: '2-digit', minute: '2-digit' })}</div>
            </div>
        `;
        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
}

console.log('📝 Step 5: Advanced features and finalization loaded - ARIA platform complete!');
console.log('🏭 Industry 4.0 and Market Intelligence modules loaded!');
console.log('🎓 Enhanced Coursera integration with 2025 trends loaded!');
console.log('🧠 Manus AI Insights module loaded!');
console.log('🤖 ARIA AI Agent system loaded!');
