<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> - Personalized Learning Platform</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/particles.js/2.0.0/particles.min.js"></script>
</head>
<body>
    <div id="particles-js"></div>

    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-graduation-cap"></i>
                <span><PERSON><PERSON><PERSON>'s Learning Hub</span>
            </div>
            <ul class="nav-menu">
                <li><a href="#dashboard" class="nav-link">Dashboard</a></li>
                <li><a href="#profile" class="nav-link">Profile</a></li>
                <li><a href="#courses" class="nav-link">Courses</a></li>
                <li><a href="#progress" class="nav-link">Progress</a></li>
                <li><a href="#roadmap" class="nav-link">Roadmap</a></li>
            </ul>
            <div class="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="hero" class="hero">
        <div class="hero-content">
            <div class="hero-text">
                <h1 class="hero-title">Welcome, Dr. Andrii Zinchuk</h1>
                <p class="hero-subtitle">Your Personalized Learning Journey in Materials Engineering, AI & Data Science</p>
                <div class="hero-stats">
                    <div class="stat">
                        <span class="stat-number">15+</span>
                        <span class="stat-label">Years Experience</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number">5</span>
                        <span class="stat-label">Certifications</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number">3</span>
                        <span class="stat-label">Languages</span>
                    </div>
                </div>
                <button class="cta-button" onclick="generateCourses()">
                    <i class="fas fa-rocket"></i>
                    Start Learning Journey
                </button>
            </div>
            <div class="hero-image">
                <div class="profile-card">
                    <img src="https://via.placeholder.com/200x200/4a90e2/ffffff?text=AZ" alt="Andrii Zinchuk" class="profile-img">
                    <h3>Dr. Andrii Zinchuk</h3>
                    <p>Process Engineer SMT/CBA at Aptiv</p>
                    <div class="skills-preview">
                        <span class="skill-tag">Materials Science</span>
                        <span class="skill-tag">PVD Coating</span>
                        <span class="skill-tag">AI & Data Analysis</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Dashboard Section -->
    <section id="dashboard" class="dashboard">
        <div class="container">
            <h2 class="section-title">Learning Dashboard</h2>
            <div class="dashboard-grid">
                <!-- Current Learning Status -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-chart-line"></i> Learning Progress</h3>
                    </div>
                    <div class="card-content">
                        <canvas id="progressChart"></canvas>
                    </div>
                </div>

                <!-- Recommended Courses -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-lightbulb"></i> AI Recommendations</h3>
                    </div>
                    <div class="card-content" id="recommendations">
                        <div class="loading">Analyzing your profile...</div>
                    </div>
                </div>

                <!-- Skill Assessment -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-brain"></i> Skill Matrix</h3>
                    </div>
                    <div class="card-content">
                        <canvas id="skillChart"></canvas>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-clock"></i> Recent Activity</h3>
                    </div>
                    <div class="card-content">
                        <div class="activity-list" id="activityList">
                            <!-- Activities will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Profile Section -->
    <section id="profile" class="profile-section">
        <div class="container">
            <h2 class="section-title">Professional Profile Analysis</h2>
            <div class="profile-grid">
                <div class="profile-info">
                    <div class="info-card">
                        <h3><i class="fas fa-user"></i> Background</h3>
                        <div class="info-content">
                            <p><strong>Current Role:</strong> Process Engineer SMT/CBA at Aptiv</p>
                            <p><strong>Education:</strong> Ph.D. in Metallic Materials Engineering and Ceramics</p>
                            <p><strong>Experience:</strong> 15+ years in Materials Engineering, Metallurgy, and Automotive</p>
                            <p><strong>Specializations:</strong> PVD Coating, Surface Technology, Quality Engineering</p>
                        </div>
                    </div>

                    <div class="info-card">
                        <h3><i class="fas fa-certificate"></i> Current Certifications</h3>
                        <div class="certification-list">
                            <div class="cert-item">
                                <i class="fas fa-check-circle"></i>
                                <span>SS&C Blue Prism RPA Professional Certificate</span>
                            </div>
                            <div class="cert-item">
                                <i class="fas fa-check-circle"></i>
                                <span>Programming for Everybody (Python)</span>
                            </div>
                            <div class="cert-item">
                                <i class="fas fa-check-circle"></i>
                                <span>Learning SAP MM (Materials Management)</span>
                            </div>
                            <div class="cert-item">
                                <i class="fas fa-check-circle"></i>
                                <span>The Growth Mindset</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="skills-analysis">
                    <div class="info-card">
                        <h3><i class="fas fa-cogs"></i> Technical Skills</h3>
                        <div class="skills-grid">
                            <div class="skill-category">
                                <h4>Engineering</h4>
                                <div class="skill-bar">
                                    <span>Materials Science</span>
                                    <div class="bar"><div class="fill" style="width: 95%"></div></div>
                                </div>
                                <div class="skill-bar">
                                    <span>PVD Coating</span>
                                    <div class="bar"><div class="fill" style="width: 90%"></div></div>
                                </div>
                                <div class="skill-bar">
                                    <span>Quality Engineering</span>
                                    <div class="bar"><div class="fill" style="width: 88%"></div></div>
                                </div>
                            </div>

                            <div class="skill-category">
                                <h4>Technology</h4>
                                <div class="skill-bar">
                                    <span>Python Programming</span>
                                    <div class="bar"><div class="fill" style="width: 70%"></div></div>
                                </div>
                                <div class="skill-bar">
                                    <span>Data Analysis</span>
                                    <div class="bar"><div class="fill" style="width: 65%"></div></div>
                                </div>
                                <div class="skill-bar">
                                    <span>AI/ML</span>
                                    <div class="bar"><div class="fill" style="width: 60%"></div></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Courses Section -->
    <section id="courses" class="courses-section">
        <div class="container">
            <h2 class="section-title">Personalized Course Recommendations</h2>

            <!-- Course Management Controls -->
            <div class="course-management">
                <div class="management-controls">
                    <button class="add-course-btn" onclick="openAddCourseModal()">
                        <i class="fas fa-plus"></i> Add New Course
                    </button>
                    <button class="import-coursera-btn" onclick="importCourseraTopCourses()">
                        <i class="fab fa-coursera"></i> Import Top Coursera Courses
                    </button>
                    <button class="refresh-recommendations-btn" onclick="refreshRecommendations()">
                        <i class="fas fa-sync-alt"></i> Refresh AI Recommendations
                    </button>
                </div>

                <div class="course-stats">
                    <div class="stat-item">
                        <span class="stat-label">Total Courses:</span>
                        <span class="stat-value" id="totalCourses">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Enrolled:</span>
                        <span class="stat-value" id="enrolledCourses">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Completed:</span>
                        <span class="stat-value" id="completedCourses">0</span>
                    </div>
                </div>
            </div>

            <div class="course-filters">
                <button class="filter-btn active" data-filter="all">All Courses</button>
                <button class="filter-btn" data-filter="ai-ml">AI & Machine Learning</button>
                <button class="filter-btn" data-filter="data-science">Data Science</button>
                <button class="filter-btn" data-filter="engineering">Engineering</button>
                <button class="filter-btn" data-filter="business">Business Intelligence</button>
                <button class="filter-btn" data-filter="coursera">Coursera Top</button>
            </div>
            <div class="courses-grid" id="coursesGrid">
                <!-- Courses will be populated by JavaScript -->
            </div>
        </div>
    </section>

    <!-- Progress Section -->
    <section id="progress" class="progress-section">
        <div class="container">
            <h2 class="section-title">Learning Progress & Analytics</h2>
            <div class="progress-grid">
                <div class="progress-card">
                    <h3><i class="fas fa-trophy"></i> Achievements</h3>
                    <div class="achievements-list">
                        <div class="achievement">
                            <i class="fas fa-medal"></i>
                            <span>Python Fundamentals Completed</span>
                        </div>
                        <div class="achievement">
                            <i class="fas fa-star"></i>
                            <span>Data Analysis Beginner</span>
                        </div>
                        <div class="achievement">
                            <i class="fas fa-certificate"></i>
                            <span>RPA Professional</span>
                        </div>
                    </div>
                </div>

                <div class="progress-card">
                    <h3><i class="fas fa-chart-area"></i> Learning Analytics</h3>
                    <canvas id="learningChart"></canvas>
                </div>
            </div>
        </div>
    </section>

    <!-- Roadmap Section -->
    <section id="roadmap" class="roadmap-section">
        <div class="container">
            <h2 class="section-title">Career Development Roadmap</h2>
            <div class="roadmap-container">
                <div class="roadmap-path" id="roadmapPath">
                    <!-- Roadmap will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </section>

    <!-- Course Management Modal -->
    <div id="courseModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">Add New Course</h2>
                <span class="close" onclick="closeCourseModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="courseForm">
                    <div class="form-group">
                        <label for="courseTitle">Course Title *</label>
                        <input type="text" id="courseTitle" name="title" required>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="courseProvider">Provider *</label>
                            <select id="courseProvider" name="provider" required>
                                <option value="">Select Provider</option>
                                <option value="Coursera">Coursera</option>
                                <option value="edX">edX</option>
                                <option value="MIT OpenCourseWare">MIT OpenCourseWare</option>
                                <option value="DataCamp">DataCamp</option>
                                <option value="Udacity">Udacity</option>
                                <option value="LinkedIn Learning">LinkedIn Learning</option>
                                <option value="Pluralsight">Pluralsight</option>
                                <option value="Other">Other</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="courseCategory">Category *</label>
                            <select id="courseCategory" name="category" required>
                                <option value="">Select Category</option>
                                <option value="ai-ml">AI & Machine Learning</option>
                                <option value="data-science">Data Science</option>
                                <option value="engineering">Engineering</option>
                                <option value="business">Business Intelligence</option>
                                <option value="leadership">Leadership</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="courseLevel">Level *</label>
                            <select id="courseLevel" name="level" required>
                                <option value="">Select Level</option>
                                <option value="Beginner">Beginner</option>
                                <option value="Intermediate">Intermediate</option>
                                <option value="Advanced">Advanced</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="courseDuration">Duration</label>
                            <input type="text" id="courseDuration" name="duration" placeholder="e.g., 8 weeks">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="courseRating">Rating (1-5)</label>
                            <input type="number" id="courseRating" name="rating" min="1" max="5" step="0.1" placeholder="4.5">
                        </div>

                        <div class="form-group">
                            <label for="courseRelevance">Relevance % (for Andrii)</label>
                            <input type="number" id="courseRelevance" name="relevance" min="0" max="100" placeholder="85">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="courseDescription">Description *</label>
                        <textarea id="courseDescription" name="description" rows="3" required placeholder="Brief description of the course content and benefits"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="courseSkills">Skills (comma-separated)</label>
                        <input type="text" id="courseSkills" name="skills" placeholder="Python, Machine Learning, Data Analysis">
                    </div>

                    <div class="form-group">
                        <label for="courseUrl">Course URL</label>
                        <input type="url" id="courseUrl" name="url" placeholder="https://www.coursera.org/learn/...">
                    </div>

                    <div class="form-group">
                        <label for="courseStatus">Status</label>
                        <select id="courseStatus" name="status">
                            <option value="available">Available</option>
                            <option value="enrolled">Enrolled</option>
                            <option value="in-progress">In Progress</option>
                            <option value="completed">Completed</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeCourseModal()">Cancel</button>
                <button type="button" class="btn-primary" onclick="saveCourse()">Save Course</button>
            </div>
        </div>
    </div>

    <!-- Coursera Import Modal -->
    <div id="courseraModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Import Top Coursera Courses</h2>
                <span class="close" onclick="closeCourseraModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="coursera-categories">
                    <h3>Select Categories to Import:</h3>
                    <div class="category-checkboxes">
                        <label class="checkbox-label">
                            <input type="checkbox" value="ai-ml" checked> AI & Machine Learning
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" value="data-science" checked> Data Science
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" value="engineering"> Engineering
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" value="business"> Business Intelligence
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" value="leadership"> Leadership
                        </label>
                    </div>
                </div>
                <div class="import-progress" id="importProgress" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <p id="progressText">Importing courses...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeCourseraModal()">Cancel</button>
                <button type="button" class="btn-primary" onclick="startCourseraImport()">Import Selected</button>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Andrii's Learning Platform</h3>
                    <p>Powered by AI and designed for continuous professional development</p>
                </div>
                <div class="footer-section">
                    <h3>Contact</h3>
                    <p>Email: <EMAIL></p>
                    <p>LinkedIn: andrii-zinchuk-980121ba</p>
                </div>
                <div class="footer-section">
                    <h3>Technologies</h3>
                    <p>Manus AI • Data Analytics • Machine Learning</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 Personalized Learning Platform. Enhanced with Manus AI capabilities.</p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
    <script src="course-manager.js"></script>
</body>
</html>
