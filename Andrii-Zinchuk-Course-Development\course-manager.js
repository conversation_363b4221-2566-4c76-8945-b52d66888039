// Course Management System for <PERSON><PERSON><PERSON> Learning Platform
// Enhanced with Coursera integration and dynamic course management

// Global course storage
let allCourses = [];
let currentEditingCourse = null;

// Top Coursera courses specifically relevant for <PERSON><PERSON><PERSON>
const topCourseraCourses = {
    "ai-ml": [
        {
            title: "Machine Learning Specialization",
            provider: "Coursera",
            category: "ai-ml",
            level: "Intermediate",
            duration: "11 weeks",
            rating: 4.9,
            description: "Master machine learning fundamentals with <PERSON>. Perfect for engineers transitioning to AI.",
            relevance: 98,
            skills: ["Machine Learning", "Python", "Neural Networks", "Deep Learning"],
            url: "https://www.coursera.org/specializations/machine-learning-introduction",
            instructor: "<PERSON>",
            university: "Stanford University",
            status: "available"
        },
        {
            title: "AI for Manufacturing",
            provider: "Coursera",
            category: "ai-ml",
            level: "Advanced",
            duration: "8 weeks",
            rating: 4.7,
            description: "Apply AI techniques to manufacturing processes, quality control, and predictive maintenance.",
            relevance: 96,
            skills: ["AI", "Manufacturing", "Quality Control", "Predictive Analytics"],
            url: "https://www.coursera.org/learn/ai-manufacturing",
            instructor: "Industry Experts",
            university: "University of Pennsylvania",
            status: "available"
        },
        {
            title: "Deep Learning Specialization",
            provider: "Coursera",
            category: "ai-ml",
            level: "Advanced",
            duration: "16 weeks",
            rating: 4.8,
            description: "Master deep learning with hands-on projects in computer vision and NLP.",
            relevance: 92,
            skills: ["Deep Learning", "TensorFlow", "CNN", "RNN"],
            url: "https://www.coursera.org/specializations/deep-learning",
            instructor: "Andrew Ng",
            university: "deeplearning.ai",
            status: "available"
        }
    ],
    "data-science": [
        {
            title: "Google Data Analytics Professional Certificate",
            provider: "Coursera",
            category: "data-science",
            level: "Beginner",
            duration: "6 months",
            rating: 4.6,
            description: "Complete data analytics program with hands-on projects and job-ready skills.",
            relevance: 94,
            skills: ["Data Analysis", "SQL", "Tableau", "R Programming"],
            url: "https://www.coursera.org/professional-certificates/google-data-analytics",
            instructor: "Google Career Certificates",
            university: "Google",
            status: "available"
        },
        {
            title: "IBM Data Science Professional Certificate",
            provider: "Coursera",
            category: "data-science",
            level: "Intermediate",
            duration: "10 months",
            rating: 4.5,
            description: "Comprehensive data science program with Python, machine learning, and data visualization.",
            relevance: 91,
            skills: ["Python", "Data Science", "Machine Learning", "Data Visualization"],
            url: "https://www.coursera.org/professional-certificates/ibm-data-science",
            instructor: "IBM Skills Network",
            university: "IBM",
            status: "available"
        },
        {
            title: "Applied Data Science with Python Specialization",
            provider: "Coursera",
            category: "data-science",
            level: "Intermediate",
            duration: "5 months",
            rating: 4.4,
            description: "Learn data science through hands-on Python programming and real-world projects.",
            relevance: 89,
            skills: ["Python", "Pandas", "Matplotlib", "Scikit-learn"],
            url: "https://www.coursera.org/specializations/data-science-python",
            instructor: "University of Michigan",
            university: "University of Michigan",
            status: "available"
        }
    ],
    "engineering": [
        {
            title: "Digital Manufacturing & Design Technology",
            provider: "Coursera",
            category: "engineering",
            level: "Advanced",
            duration: "12 weeks",
            rating: 4.6,
            description: "Explore Industry 4.0 technologies and digital transformation in manufacturing.",
            relevance: 95,
            skills: ["Industry 4.0", "Digital Manufacturing", "IoT", "Automation"],
            url: "https://www.coursera.org/specializations/digital-manufacturing-design-technology",
            instructor: "University at Buffalo",
            university: "University at Buffalo",
            status: "available"
        },
        {
            title: "Materials Science: 10 Things Every Engineer Should Know",
            provider: "Coursera",
            category: "engineering",
            level: "Intermediate",
            duration: "6 weeks",
            rating: 4.7,
            description: "Advanced materials science concepts for modern engineering applications.",
            relevance: 97,
            skills: ["Materials Science", "Engineering", "Nanotechnology", "Composites"],
            url: "https://www.coursera.org/learn/materials-science",
            instructor: "University of California, Davis",
            university: "UC Davis",
            status: "available"
        }
    ],
    "business": [
        {
            title: "Business Intelligence and Data Analytics",
            provider: "Coursera",
            category: "business",
            level: "Intermediate",
            duration: "8 weeks",
            rating: 4.5,
            description: "Transform technical expertise into business insights and strategic decisions.",
            relevance: 88,
            skills: ["Business Intelligence", "Data Analytics", "Strategy", "Decision Making"],
            url: "https://www.coursera.org/learn/business-intelligence-data-analytics",
            instructor: "University of Colorado Boulder",
            university: "University of Colorado Boulder",
            status: "available"
        },
        {
            title: "Leading People and Teams Specialization",
            provider: "Coursera",
            category: "business",
            level: "Intermediate",
            duration: "6 months",
            rating: 4.6,
            description: "Develop leadership skills for technical professionals moving into management.",
            relevance: 85,
            skills: ["Leadership", "Team Management", "Communication", "Strategy"],
            url: "https://www.coursera.org/specializations/leading-teams",
            instructor: "University of Michigan",
            university: "University of Michigan",
            status: "available"
        }
    ]
};

// Initialize course management system
function initializeCourseManager() {
    loadCoursesFromStorage();
    updateCourseStats();
    generateCourseRecommendations();
}

// Load courses from localStorage
function loadCoursesFromStorage() {
    const savedCourses = localStorage.getItem('andriiCourses');
    if (savedCourses) {
        allCourses = JSON.parse(savedCourses);
    } else {
        // Initialize with default courses if none exist
        allCourses = [...courseRecommendations];
        saveCoursesToStorage();
    }
}

// Save courses to localStorage
function saveCoursesToStorage() {
    localStorage.setItem('andriiCourses', JSON.stringify(allCourses));
}

// Update course statistics
function updateCourseStats() {
    const totalCourses = allCourses.length;
    const enrolledCourses = allCourses.filter(course => 
        course.status === 'enrolled' || course.status === 'in-progress'
    ).length;
    const completedCourses = allCourses.filter(course => 
        course.status === 'completed'
    ).length;

    document.getElementById('totalCourses').textContent = totalCourses;
    document.getElementById('enrolledCourses').textContent = enrolledCourses;
    document.getElementById('completedCourses').textContent = completedCourses;
}

// Open add course modal
function openAddCourseModal() {
    currentEditingCourse = null;
    document.getElementById('modalTitle').textContent = 'Add New Course';
    document.getElementById('courseForm').reset();
    document.getElementById('courseModal').style.display = 'block';
}

// Open edit course modal
function openEditCourseModal(courseIndex) {
    currentEditingCourse = courseIndex;
    const course = allCourses[courseIndex];
    
    document.getElementById('modalTitle').textContent = 'Edit Course';
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseProvider').value = course.provider;
    document.getElementById('courseCategory').value = course.category;
    document.getElementById('courseLevel').value = course.level;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseRating').value = course.rating;
    document.getElementById('courseRelevance').value = course.relevance;
    document.getElementById('courseDescription').value = course.description;
    document.getElementById('courseSkills').value = course.skills.join(', ');
    document.getElementById('courseUrl').value = course.url;
    document.getElementById('courseStatus').value = course.status || 'available';
    
    document.getElementById('courseModal').style.display = 'block';
}

// Close course modal
function closeCourseModal() {
    document.getElementById('courseModal').style.display = 'none';
    currentEditingCourse = null;
}

// Save course (add or edit)
function saveCourse() {
    const form = document.getElementById('courseForm');
    const formData = new FormData(form);
    
    const course = {
        title: formData.get('title'),
        provider: formData.get('provider'),
        category: formData.get('category'),
        level: formData.get('level'),
        duration: formData.get('duration') || 'Not specified',
        rating: parseFloat(formData.get('rating')) || 4.0,
        relevance: parseInt(formData.get('relevance')) || 75,
        description: formData.get('description'),
        skills: formData.get('skills').split(',').map(skill => skill.trim()).filter(skill => skill),
        url: formData.get('url') || '#',
        status: formData.get('status') || 'available',
        id: currentEditingCourse !== null ? allCourses[currentEditingCourse].id : Date.now()
    };

    if (currentEditingCourse !== null) {
        // Edit existing course
        allCourses[currentEditingCourse] = course;
        showNotification('Course updated successfully!', 'success');
    } else {
        // Add new course
        allCourses.push(course);
        showNotification('Course added successfully!', 'success');
    }

    saveCoursesToStorage();
    updateCourseStats();
    generateCourseRecommendations();
    closeCourseModal();
}

// Delete course
function deleteCourse(courseIndex) {
    if (confirm('Are you sure you want to delete this course?')) {
        const course = allCourses[courseIndex];
        allCourses.splice(courseIndex, 1);
        saveCoursesToStorage();
        updateCourseStats();
        generateCourseRecommendations();
        showNotification(`"${course.title}" has been deleted.`, 'success');
    }
}

// Open Coursera import modal
function importCourseraTopCourses() {
    document.getElementById('courseraModal').style.display = 'block';
}

// Close Coursera modal
function closeCourseraModal() {
    document.getElementById('courseraModal').style.display = 'none';
    document.getElementById('importProgress').style.display = 'none';
    document.getElementById('progressFill').style.width = '0%';
}

// Start Coursera import
function startCourseraImport() {
    const checkboxes = document.querySelectorAll('.category-checkboxes input[type="checkbox"]:checked');
    const selectedCategories = Array.from(checkboxes).map(cb => cb.value);
    
    if (selectedCategories.length === 0) {
        showNotification('Please select at least one category to import.', 'error');
        return;
    }

    document.getElementById('importProgress').style.display = 'block';
    
    let importedCount = 0;
    let totalToImport = 0;
    
    // Calculate total courses to import
    selectedCategories.forEach(category => {
        if (topCourseraCourses[category]) {
            totalToImport += topCourseraCourses[category].length;
        }
    });

    // Simulate import process
    selectedCategories.forEach((category, categoryIndex) => {
        if (topCourseraCourses[category]) {
            topCourseraCourses[category].forEach((course, courseIndex) => {
                setTimeout(() => {
                    // Check if course already exists
                    const existingCourse = allCourses.find(c => 
                        c.title === course.title && c.provider === course.provider
                    );
                    
                    if (!existingCourse) {
                        allCourses.push({
                            ...course,
                            id: Date.now() + Math.random()
                        });
                    }
                    
                    importedCount++;
                    const progress = (importedCount / totalToImport) * 100;
                    document.getElementById('progressFill').style.width = progress + '%';
                    document.getElementById('progressText').textContent = 
                        `Importing courses... ${importedCount}/${totalToImport}`;
                    
                    if (importedCount === totalToImport) {
                        setTimeout(() => {
                            saveCoursesToStorage();
                            updateCourseStats();
                            generateCourseRecommendations();
                            closeCourseraModal();
                            showNotification(`Successfully imported ${importedCount} courses from Coursera!`, 'success');
                        }, 500);
                    }
                }, (categoryIndex * topCourseraCourses[category].length + courseIndex) * 200);
            });
        }
    });
}

// Refresh AI recommendations
function refreshRecommendations() {
    showNotification('Manus AI is analyzing your profile and refreshing recommendations...', 'info');
    
    // Simulate AI analysis
    setTimeout(() => {
        // Recalculate relevance scores based on Andrii's profile
        allCourses.forEach(course => {
            course.relevance = calculateRelevanceScore(course);
        });
        
        // Sort by relevance
        allCourses.sort((a, b) => b.relevance - a.relevance);
        
        saveCoursesToStorage();
        generateCourseRecommendations();
        showNotification('AI recommendations updated successfully!', 'success');
    }, 2000);
}

// Calculate relevance score based on Andrii's profile
function calculateRelevanceScore(course) {
    let score = 50; // Base score
    
    // Boost for materials science and engineering
    if (course.skills.some(skill => 
        skill.toLowerCase().includes('material') || 
        skill.toLowerCase().includes('engineering') ||
        skill.toLowerCase().includes('quality')
    )) {
        score += 20;
    }
    
    // Boost for AI/ML and data science
    if (course.category === 'ai-ml' || course.category === 'data-science') {
        score += 15;
    }
    
    // Boost for Python and programming
    if (course.skills.some(skill => 
        skill.toLowerCase().includes('python') || 
        skill.toLowerCase().includes('programming')
    )) {
        score += 10;
    }
    
    // Boost for business and leadership (career growth)
    if (course.category === 'business' || course.category === 'leadership') {
        score += 8;
    }
    
    // Boost for manufacturing and automotive
    if (course.skills.some(skill => 
        skill.toLowerCase().includes('manufacturing') || 
        skill.toLowerCase().includes('automotive') ||
        skill.toLowerCase().includes('industry 4.0')
    )) {
        score += 12;
    }
    
    return Math.min(score, 100); // Cap at 100
}

// Enhanced course generation with management features
function generateCourseRecommendations() {
    const coursesGrid = document.getElementById('coursesGrid');
    
    if (allCourses.length === 0) {
        coursesGrid.innerHTML = `
            <div class="no-courses">
                <i class="fas fa-graduation-cap fa-3x"></i>
                <h3>No courses available</h3>
                <p>Add your first course or import from Coursera to get started!</p>
            </div>
        `;
        return;
    }

    coursesGrid.innerHTML = allCourses.map((course, index) => `
        <div class="course-card" data-category="${course.category}">
            <div class="course-status status-${course.status}">${course.status}</div>
            <div class="course-actions">
                <button class="action-btn edit-btn" onclick="openEditCourseModal(${index})" title="Edit Course">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="action-btn delete-btn" onclick="deleteCourse(${index})" title="Delete Course">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
            <div class="course-header">
                <h3>${course.title}</h3>
                <div class="course-provider">${course.provider}</div>
            </div>
            <div class="course-meta">
                <span class="level ${course.level.toLowerCase()}">${course.level}</span>
                <span class="duration">${course.duration}</span>
                <span class="rating">★ ${course.rating}</span>
            </div>
            <p class="course-description">${course.description}</p>
            <div class="course-skills">
                ${course.skills.map(skill => `<span class="skill-tag">${skill}</span>`).join('')}
            </div>
            <div class="course-footer">
                <div class="relevance-score">
                    <span>Relevance: ${course.relevance}%</span>
                    <div class="relevance-bar">
                        <div class="relevance-fill" style="width: ${course.relevance}%"></div>
                    </div>
                </div>
                <button class="enroll-btn" onclick="enrollCourse('${course.title}', ${index})">
                    <i class="fas fa-play"></i> 
                    ${course.status === 'completed' ? 'Review' : 
                      course.status === 'enrolled' || course.status === 'in-progress' ? 'Continue' : 'Start Learning'}
                </button>
            </div>
        </div>
    `).join('');
}

// Enhanced enroll function with status management
function enrollCourse(courseTitle, courseIndex) {
    const course = allCourses[courseIndex];
    
    if (course.status === 'available') {
        course.status = 'enrolled';
        showNotification(`Enrolled in "${courseTitle}"!`, 'success');
    } else if (course.status === 'enrolled') {
        course.status = 'in-progress';
        showNotification(`Continuing "${courseTitle}"...`, 'info');
    } else if (course.status === 'in-progress') {
        course.status = 'completed';
        showNotification(`Congratulations! You completed "${courseTitle}"!`, 'success');
    } else {
        showNotification(`Reviewing "${courseTitle}"...`, 'info');
    }
    
    saveCoursesToStorage();
    updateCourseStats();
    generateCourseRecommendations();
    
    // Update activity list
    const activityList = document.getElementById('activityList');
    if (activityList) {
        const newActivity = document.createElement('div');
        newActivity.className = 'activity-item info';
        newActivity.innerHTML = `
            <i class="fas fa-play"></i>
            <div class="activity-content">
                <span class="activity-action">${course.status === 'completed' ? 'Completed' : 'Updated'} ${courseTitle}</span>
                <span class="activity-time">Just now</span>
            </div>
        `;
        activityList.insertBefore(newActivity, activityList.firstChild);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeCourseManager();
    
    // Close modals when clicking outside
    window.onclick = function(event) {
        const courseModal = document.getElementById('courseModal');
        const courseraModal = document.getElementById('courseraModal');
        
        if (event.target === courseModal) {
            closeCourseModal();
        }
        if (event.target === courseraModal) {
            closeCourseraModal();
        }
    };
});

// Export functions for global access
window.openAddCourseModal = openAddCourseModal;
window.openEditCourseModal = openEditCourseModal;
window.closeCourseModal = closeCourseModal;
window.saveCourse = saveCourse;
window.deleteCourse = deleteCourse;
window.importCourseraTopCourses = importCourseraTopCourses;
window.closeCourseraModal = closeCourseraModal;
window.startCourseraImport = startCourseraImport;
window.refreshRecommendations = refreshRecommendations;
window.enrollCourse = enrollCourse;
