/* AI Coach ARIA - Advanced Career Transformation Platform */
/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

/* Particles Background */
#particles-js {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: -1;
}

/* Navigation */
.navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: bold;
    color: #4a90e2;
}

.nav-logo i {
    margin-right: 10px;
    font-size: 1.8rem;
    background: linear-gradient(45deg, #4a90e2, #667eea);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 25px;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    padding: 8px 16px;
    border-radius: 20px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    background: linear-gradient(45deg, #4a90e2, #667eea);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
}

.voice-toggle {
    display: flex;
    align-items: center;
}

.voice-btn {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
    border: none;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.voice-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
}

/* Main Container */
.main-container {
    display: flex;
    margin-top: 70px;
    min-height: calc(100vh - 70px);
}

.content-area {
    flex: 1;
    padding: 40px;
    margin-right: 400px; /* Space for ARIA sidebar */
}

/* Section Management */
.section {
    display: none;
    animation: fadeInUp 0.6s ease forwards;
}

.section.active {
    display: block;
}

.section-header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

.section-header h1 {
    font-size: 2.5rem;
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.section-header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
}

.dashboard-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(45deg, #4a90e2, #667eea);
}

.dashboard-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 60px rgba(0, 0, 0, 0.15);
}

.dashboard-card h3 {
    color: #333;
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.3rem;
}

.dashboard-card h3 i {
    color: #4a90e2;
    font-size: 1.5rem;
}

/* Progress Gauge */
.progress-gauge {
    position: relative;
    width: 200px;
    height: 200px;
    margin: 0 auto 30px;
}

.gauge-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

.gauge-value {
    display: block;
    font-size: 2.5rem;
    font-weight: bold;
    color: #4a90e2;
}

.gauge-label {
    font-size: 0.9rem;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.progress-details {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: rgba(74, 144, 226, 0.05);
    border-radius: 8px;
}

.detail-item span:first-child {
    font-weight: 600;
    color: #555;
}

.detail-item span:last-child {
    color: #4a90e2;
    font-weight: 500;
}

/* Skills Radar */
.skills-legend {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-top: 20px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.legend-item.current .legend-color {
    background: #4a90e2;
}

.legend-item.target .legend-color {
    background: #ff6b6b;
}

/* Salary Details */
.salary-details {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 20px;
}

.salary-current,
.salary-target,
.salary-growth {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border-radius: 8px;
}

.salary-current {
    background: rgba(108, 117, 125, 0.1);
}

.salary-target {
    background: rgba(74, 144, 226, 0.1);
}

.salary-growth {
    background: rgba(46, 204, 113, 0.1);
}

.salary-details .label {
    font-weight: 600;
    color: #555;
}

.salary-details .value {
    font-weight: bold;
    color: #333;
}

.salary-details .value.positive {
    color: #2ecc71;
}

/* Action Buttons */
.action-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 20px;
    border: none;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    text-align: center;
}

.action-btn i {
    font-size: 1.5rem;
}

.action-btn.primary {
    background: linear-gradient(45deg, #4a90e2, #357abd);
    color: white;
}

.action-btn.secondary {
    background: linear-gradient(45deg, #2ecc71, #27ae60);
    color: white;
}

.action-btn.tertiary {
    background: linear-gradient(45deg, #f39c12, #e67e22);
    color: white;
}

.action-btn.quaternary {
    background: linear-gradient(45deg, #9b59b6, #8e44ad);
    color: white;
}

.action-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Achievements List */
.achievements-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 20px;
}

.achievement-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: linear-gradient(45deg, #4a90e2, #667eea);
    color: white;
    border-radius: 12px;
    animation: achievementSlideIn 0.5s ease;
}

.achievement-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.achievement-content h4 {
    margin-bottom: 5px;
    font-size: 1.1rem;
}

.achievement-content p {
    font-size: 0.9rem;
    opacity: 0.9;
}

.view-all-btn {
    width: 100%;
    padding: 12px;
    background: transparent;
    border: 2px solid #4a90e2;
    color: #4a90e2;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.view-all-btn:hover {
    background: #4a90e2;
    color: white;
}

/* Timeline Preview */
.timeline-preview {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.timeline-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: rgba(74, 144, 226, 0.05);
    border-radius: 10px;
    border-left: 4px solid #4a90e2;
}

.timeline-date {
    background: #4a90e2;
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    white-space: nowrap;
}

.timeline-content h4 {
    color: #333;
    margin-bottom: 5px;
}

.timeline-content p {
    color: #666;
    font-size: 0.9rem;
}

/* ARIA Sidebar */
.aria-sidebar {
    position: fixed;
    right: 0;
    top: 70px;
    width: 400px;
    height: calc(100vh - 70px);
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(15px);
    border-left: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    flex-direction: column;
    box-shadow: -5px 0 30px rgba(0, 0, 0, 0.1);
}

.aria-header {
    padding: 30px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 15px;
}

.aria-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(45deg, #4a90e2, #667eea);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.aria-info h3 {
    color: #333;
    margin-bottom: 5px;
}

.aria-info p {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 10px;
}

.aria-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.8rem;
    color: #2ecc71;
    font-weight: 600;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #2ecc71;
    animation: pulse 2s infinite;
}

/* Chat Interface */
.aria-chat {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.chat-message {
    max-width: 80%;
    padding: 12px 16px;
    border-radius: 18px;
    animation: messageSlideIn 0.3s ease;
}

.chat-message.aria {
    background: linear-gradient(45deg, #4a90e2, #667eea);
    color: white;
    align-self: flex-start;
    border-bottom-left-radius: 6px;
}

.chat-message.user {
    background: #f8f9fa;
    color: #333;
    align-self: flex-end;
    border-bottom-right-radius: 6px;
}

.message-time {
    font-size: 0.7rem;
    opacity: 0.7;
    margin-top: 5px;
}

/* Quick Replies */
.quick-replies {
    padding: 15px 20px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.quick-reply-btn {
    background: rgba(74, 144, 226, 0.1);
    color: #4a90e2;
    border: 1px solid rgba(74, 144, 226, 0.3);
    padding: 8px 12px;
    border-radius: 15px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.quick-reply-btn:hover {
    background: #4a90e2;
    color: white;
}

/* Chat Input */
.chat-input-container {
    padding: 20px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.chat-input {
    display: flex;
    align-items: center;
    gap: 10px;
    background: #f8f9fa;
    border-radius: 25px;
    padding: 5px;
}

.chat-input input {
    flex: 1;
    border: none;
    background: transparent;
    padding: 12px 16px;
    font-size: 0.9rem;
    outline: none;
}

.send-btn,
.voice-input-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.send-btn {
    background: linear-gradient(45deg, #4a90e2, #667eea);
    color: white;
}

.voice-input-btn {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
}

.send-btn:hover,
.voice-input-btn:hover {
    transform: scale(1.1);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 20px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 25px 80px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease;
}

.modal-header {
    background: linear-gradient(45deg, #4a90e2, #667eea);
    color: white;
    padding: 25px;
    border-radius: 20px 20px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.close {
    color: white;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover {
    color: #ff6b6b;
}

.modal-body {
    padding: 30px;
}

.modal-footer {
    padding: 20px 30px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
}

/* Button Styles */
.btn-primary,
.btn-secondary {
    padding: 12px 25px;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.btn-primary {
    background: linear-gradient(45deg, #4a90e2, #667eea);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(74, 144, 226, 0.4);
}

.btn-secondary {
    background: #f8f9fa;
    color: #333;
    border: 2px solid #e0e0e0;
}

.btn-secondary:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes achievementSlideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .content-area {
        margin-right: 0;
        padding: 20px;
    }

    .aria-sidebar {
        display: none;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .section-header h1 {
        font-size: 2rem;
    }

    .dashboard-card {
        padding: 20px;
    }

    .action-buttons {
        grid-template-columns: 1fr;
    }
}

/* Step 2: Additional Dashboard Styles */

/* Achievement Modal Styles */
.achievement-modal .modal-content {
    max-width: 700px;
}

.achievement-showcase {
    text-align: center;
    margin-bottom: 30px;
}

.achievement-icon-large {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(45deg, #4a90e2, #667eea);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: white;
    font-size: 2rem;
}

.achievement-showcase h3 {
    color: #333;
    margin-bottom: 10px;
    font-size: 1.5rem;
}

.achievement-showcase p {
    color: #666;
    margin-bottom: 15px;
}

.achievement-date {
    background: rgba(74, 144, 226, 0.1);
    color: #4a90e2;
    padding: 8px 16px;
    border-radius: 20px;
    display: inline-block;
    font-size: 0.9rem;
    font-weight: 600;
}

/* Impact Analysis Styles */
.impact-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: rgba(74, 144, 226, 0.05);
    border-radius: 8px;
    margin-bottom: 10px;
}

.impact-label {
    font-weight: 600;
    color: #555;
}

.impact-value {
    color: #4a90e2;
    font-weight: bold;
}

/* Next Steps Styles */
.next-steps-list {
    list-style: none;
    padding: 0;
}

.next-steps-list li {
    padding: 12px;
    background: rgba(46, 204, 113, 0.1);
    border-radius: 8px;
    margin-bottom: 10px;
    position: relative;
    padding-left: 40px;
}

.next-steps-list li::before {
    content: '✓';
    position: absolute;
    left: 15px;
    color: #2ecc71;
    font-weight: bold;
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 90px;
    right: 20px;
    background: white;
    padding: 15px 20px;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    gap: 10px;
    z-index: 10000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    max-width: 350px;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-left: 4px solid #2ecc71;
    color: #2ecc71;
}

.notification.info {
    border-left: 4px solid #4a90e2;
    color: #4a90e2;
}

.notification.error {
    border-left: 4px solid #e74c3c;
    color: #e74c3c;
}

.notification i {
    font-size: 1.2rem;
}

/* Voice Modal Styles */
.voice-modal .modal-content {
    max-width: 500px;
}

.voice-interface {
    text-align: center;
    padding: 20px;
}

.voice-animation {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin: 30px 0;
    height: 60px;
}

.voice-circle {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #4a90e2;
    animation: voicePulse 1.5s ease-in-out infinite;
}

.voice-circle:nth-child(2) {
    animation-delay: 0.2s;
}

.voice-circle:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes voicePulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.7;
    }
    50% {
        transform: scale(1.5);
        opacity: 1;
    }
}

#voiceStatus {
    color: #666;
    margin-bottom: 30px;
    font-size: 1.1rem;
}

.voice-controls {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 30px;
}

.voice-control-btn {
    background: linear-gradient(45deg, #4a90e2, #667eea);
    color: white;
    border: none;
    padding: 15px 25px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
}

.voice-control-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(74, 144, 226, 0.4);
}

.voice-control-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.voice-transcript {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    min-height: 100px;
    text-align: left;
    color: #333;
    font-style: italic;
}

/* Enhanced Chart Containers */
.progress-gauge canvas,
.skills-card canvas,
.salary-card canvas {
    max-height: 250px;
}

/* Timeline Item Hover Effects */
.timeline-item:hover {
    background: rgba(74, 144, 226, 0.1);
    transform: translateX(5px);
    transition: all 0.3s ease;
}

/* Achievement Item Click Effects */
.achievement-item {
    cursor: pointer;
    transition: all 0.3s ease;
}

.achievement-item:hover {
    transform: scale(1.02);
    box-shadow: 0 8px 25px rgba(74, 144, 226, 0.3);
}

/* Action Button Loading States */
.action-btn.loading {
    position: relative;
    color: transparent;
}

.action-btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Enhanced Mobile Responsiveness */
@media (max-width: 480px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .dashboard-card {
        padding: 20px;
    }

    .progress-gauge {
        width: 150px;
        height: 150px;
    }

    .gauge-value {
        font-size: 2rem;
    }

    .action-buttons {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .action-btn {
        padding: 15px;
    }

    .notification {
        right: 10px;
        left: 10px;
        max-width: none;
    }
}

/* Step 3: Courses, Certifications and Portfolio Styles */

/* Courses Section */
.courses-grid,
.certifications-grid,
.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 30px;
    max-width: 1400px;
    margin: 0 auto;
}

.course-card,
.certification-card,
.portfolio-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.course-card::before,
.certification-card::before,
.portfolio-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(45deg, #4a90e2, #667eea);
}

.course-card:hover,
.certification-card:hover,
.portfolio-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 60px rgba(0, 0, 0, 0.15);
}

/* Course Status Badges */
.course-status-badge {
    position: absolute;
    top: 20px;
    right: 20px;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.course-status-badge.recommended {
    background: linear-gradient(45deg, #2ecc71, #27ae60);
    color: white;
}

.course-status-badge.in-progress {
    background: linear-gradient(45deg, #f39c12, #e67e22);
    color: white;
}

.course-status-badge.planned {
    background: linear-gradient(45deg, #3498db, #2980b9);
    color: white;
}

.course-status-badge.available {
    background: linear-gradient(45deg, #9b59b6, #8e44ad);
    color: white;
}

/* Course Headers */
.course-header h3,
.cert-info h3,
.platform-info h3 {
    color: #333;
    margin-bottom: 10px;
    font-size: 1.4rem;
    line-height: 1.3;
}

.course-meta {
    display: flex;
    flex-direction: column;
    gap: 5px;
    margin-bottom: 20px;
}

.instructor {
    font-weight: 600;
    color: #4a90e2;
}

.university {
    color: #666;
    font-size: 0.9rem;
}

/* Course Stats */
.course-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
    margin-bottom: 20px;
}

.stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    padding: 10px;
    background: rgba(74, 144, 226, 0.05);
    border-radius: 8px;
}

.stat i {
    color: #4a90e2;
    font-size: 1.2rem;
}

.stat span {
    font-size: 0.9rem;
    font-weight: 600;
    color: #333;
}

/* Course Description */
.course-description,
.cert-description,
.platform-description {
    color: #555;
    line-height: 1.6;
    margin-bottom: 20px;
}

/* Relevance Bar */
.course-relevance {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
}

.relevance-label {
    font-size: 0.9rem;
    font-weight: 600;
    color: #555;
    white-space: nowrap;
}

.relevance-bar {
    flex: 1;
    height: 8px;
    background: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
}

.relevance-fill {
    height: 100%;
    background: linear-gradient(45deg, #4a90e2, #667eea);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.relevance-value {
    font-weight: bold;
    color: #4a90e2;
    font-size: 0.9rem;
}

/* Skills Tags */
.course-skills {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 20px;
}

.skill-tag {
    background: rgba(74, 144, 226, 0.1);
    color: #4a90e2;
    padding: 4px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.skill-tag:hover {
    background: #4a90e2;
    color: white;
    transform: scale(1.05);
}

/* Manufacturing Insights */
.manufacturing-insight,
.manufacturing-value,
.manufacturing-projects {
    background: rgba(46, 204, 113, 0.05);
    border-left: 4px solid #2ecc71;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.manufacturing-insight h4,
.manufacturing-value h4,
.manufacturing-projects h4 {
    color: #2ecc71;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1rem;
}

.manufacturing-insight p,
.manufacturing-value p {
    color: #555;
    line-height: 1.5;
    margin: 0;
}

/* Course Actions */
.course-actions,
.cert-actions,
.platform-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.course-btn,
.cert-btn,
.platform-btn {
    flex: 1;
    min-width: 120px;
    padding: 12px 16px;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.course-btn.primary,
.cert-btn.primary,
.platform-btn.primary {
    background: linear-gradient(45deg, #4a90e2, #357abd);
    color: white;
}

.course-btn.secondary,
.cert-btn.secondary,
.platform-btn.secondary {
    background: linear-gradient(45deg, #2ecc71, #27ae60);
    color: white;
}

.course-btn.tertiary,
.cert-btn.tertiary,
.platform-btn.tertiary {
    background: linear-gradient(45deg, #f39c12, #e67e22);
    color: white;
}

.course-btn:hover,
.cert-btn:hover,
.platform-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Certification Specific Styles */
.cert-header {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 20px;
}

.cert-provider-logo {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    background: linear-gradient(45deg, #4a90e2, #667eea);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.cert-info {
    flex: 1;
}

.cert-provider {
    color: #666;
    font-size: 0.9rem;
    margin: 0;
}

.cert-cost {
    text-align: right;
}

.cost-label {
    display: block;
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 5px;
}

.cost-value {
    font-size: 1.2rem;
    font-weight: bold;
    color: #4a90e2;
}

/* Certification Metrics */
.cert-metrics {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-bottom: 20px;
}

.metric {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: rgba(74, 144, 226, 0.05);
    border-radius: 8px;
}

.metric-label {
    font-weight: 600;
    color: #555;
    font-size: 0.9rem;
}

.metric-value {
    font-weight: bold;
    font-size: 0.9rem;
}

.metric-value.roi-high {
    color: #2ecc71;
}

.metric-value.roi-medium {
    color: #f39c12;
}

.metric-value.roi-low {
    color: #e74c3c;
}

.metric-value.difficulty-beginner {
    color: #2ecc71;
}

.metric-value.difficulty-intermediate {
    color: #f39c12;
}

.metric-value.difficulty-advanced,
.metric-value.difficulty-expert {
    color: #e74c3c;
}

/* Certification Topics */
.cert-topics {
    margin-bottom: 20px;
}

.cert-topics h4 {
    color: #333;
    margin-bottom: 10px;
    font-size: 1rem;
}

.topics-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.topic-tag {
    background: rgba(156, 39, 176, 0.1);
    color: #9c27b0;
    padding: 4px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* Portfolio Platform Styles */
.platform-header {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 20px;
}

.platform-icon {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.platform-info {
    flex: 1;
}

.platform-type {
    color: #666;
    font-size: 0.9rem;
    margin: 0;
}

.platform-cost {
    text-align: right;
    font-weight: bold;
    font-size: 1.1rem;
}

.platform-cost.free {
    color: #2ecc71;
}

.platform-cost.paid {
    color: #f39c12;
}

/* Platform Stats */
.platform-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    margin-bottom: 20px;
}

.platform-stats .stat {
    flex-direction: row;
    justify-content: space-between;
    text-align: left;
}

.stat-label {
    font-weight: 600;
    color: #555;
    font-size: 0.9rem;
}

.stat-value {
    font-weight: bold;
    color: #4a90e2;
    font-size: 0.9rem;
}

/* Projects List */
.projects-list {
    list-style: none;
    padding: 0;
    margin: 10px 0 0 0;
}

.projects-list li {
    padding: 8px 0;
    border-bottom: 1px solid rgba(46, 204, 113, 0.1);
    color: #555;
    font-size: 0.9rem;
}

.projects-list li:last-child {
    border-bottom: none;
}

.projects-list li::before {
    content: '▸';
    color: #2ecc71;
    font-weight: bold;
    margin-right: 8px;
}

/* Platform Advantages */
.platform-advantages {
    margin-bottom: 20px;
}

.platform-advantages h4 {
    color: #333;
    margin-bottom: 10px;
    font-size: 1rem;
}

.advantages-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.advantage-tag {
    background: rgba(103, 58, 183, 0.1);
    color: #673ab7;
    padding: 4px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* Responsive Design for Sections */
@media (max-width: 1200px) {
    .courses-grid,
    .certifications-grid,
    .portfolio-grid {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    }
}

@media (max-width: 768px) {
    .courses-grid,
    .certifications-grid,
    .portfolio-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .course-card,
    .certification-card,
    .portfolio-card {
        padding: 20px;
    }

    .course-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .cert-metrics {
        grid-template-columns: 1fr;
    }

    .platform-stats {
        grid-template-columns: 1fr;
    }

    .course-actions,
    .cert-actions,
    .platform-actions {
        flex-direction: column;
    }

    .course-btn,
    .cert-btn,
    .platform-btn {
        min-width: auto;
    }
}

/* Step 4: Networking Events and Career Roadmap Styles */

/* Networking Events Section */
.networking-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    margin-bottom: 30px;
}

.networking-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(45deg, #667eea, #764ba2);
}

.networking-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 60px rgba(0, 0, 0, 0.15);
}

/* Event Status Badges */
.event-status-badge {
    position: absolute;
    top: 20px;
    right: 20px;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.event-status-badge.recommended {
    background: linear-gradient(45deg, #2ecc71, #27ae60);
    color: white;
}

.event-status-badge.priority {
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    color: white;
}

.event-status-badge.planned {
    background: linear-gradient(45deg, #3498db, #2980b9);
    color: white;
}

.event-status-badge.must-attend {
    background: linear-gradient(45deg, #f39c12, #e67e22);
    color: white;
}

.event-status-badge.considering {
    background: linear-gradient(45deg, #9b59b6, #8e44ad);
    color: white;
}

.event-status-badge.supporting {
    background: linear-gradient(45deg, #1abc9c, #16a085);
    color: white;
}

/* Event Header */
.event-header {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    margin-bottom: 20px;
}

.event-date {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 15px;
    border-radius: 15px;
    min-width: 80px;
    text-align: center;
}

.date-day {
    font-size: 1.8rem;
    font-weight: bold;
    line-height: 1;
}

.date-month {
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin: 5px 0;
}

.date-year {
    font-size: 0.8rem;
    opacity: 0.9;
}

.event-info {
    flex: 1;
}

.event-info h3 {
    color: #333;
    margin-bottom: 10px;
    font-size: 1.4rem;
    line-height: 1.3;
}

.event-meta {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.event-meta span {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #666;
    font-size: 0.9rem;
}

.event-meta i {
    color: #667eea;
    width: 16px;
}

.event-cost {
    text-align: right;
    font-weight: bold;
    font-size: 1.2rem;
}

.event-cost.free {
    color: #2ecc71;
}

.event-cost.paid {
    color: #f39c12;
}

/* Event Content Sections */
.event-description,
.manufacturing-focus p,
.networking-value p {
    color: #555;
    line-height: 1.6;
    margin-bottom: 20px;
}

.manufacturing-focus,
.networking-value {
    background: rgba(46, 204, 113, 0.05);
    border-left: 4px solid #2ecc71;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.manufacturing-focus h4,
.networking-value h4 {
    color: #2ecc71;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1rem;
}

/* Event Speakers */
.event-speakers {
    margin-bottom: 20px;
}

.event-speakers h4 {
    color: #333;
    margin-bottom: 10px;
    font-size: 1rem;
}

.speakers-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.speakers-list li {
    padding: 8px 0;
    border-bottom: 1px solid rgba(102, 126, 234, 0.1);
    color: #555;
    font-size: 0.9rem;
}

.speakers-list li:last-child {
    border-bottom: none;
}

.speakers-list li::before {
    content: '🎤';
    margin-right: 8px;
}

/* Event Details */
.event-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 20px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 8px;
}

.detail-label {
    font-weight: 600;
    color: #555;
    font-size: 0.9rem;
}

.detail-value {
    font-weight: bold;
    color: #667eea;
    font-size: 0.9rem;
}

/* Event Actions */
.event-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.event-btn {
    flex: 1;
    min-width: 120px;
    padding: 12px 16px;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.event-btn.primary {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
}

.event-btn.secondary {
    background: linear-gradient(45deg, #2ecc71, #27ae60);
    color: white;
}

.event-btn.tertiary {
    background: linear-gradient(45deg, #f39c12, #e67e22);
    color: white;
}

.event-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Career Roadmap Styles */
.roadmap-header {
    text-align: center;
    margin-bottom: 40px;
    padding: 30px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    border-radius: 20px;
}

.roadmap-header h2 {
    color: #333;
    margin-bottom: 10px;
    font-size: 2.2rem;
}

.roadmap-header p {
    color: #666;
    font-size: 1.1rem;
    margin-bottom: 20px;
}

.roadmap-progress {
    max-width: 400px;
    margin: 0 auto;
}

.progress-bar {
    width: 100%;
    height: 12px;
    background: rgba(102, 126, 234, 0.2);
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 6px;
    transition: width 0.3s ease;
}

.progress-text {
    color: #667eea;
    font-weight: 600;
    font-size: 0.9rem;
}

/* Roadmap Phases */
.roadmap-phases {
    margin-bottom: 40px;
}

.phase-section {
    margin-bottom: 50px;
}

.phase-title {
    color: #333;
    font-size: 1.8rem;
    margin-bottom: 30px;
    text-align: center;
    position: relative;
}

.phase-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 3px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 2px;
}

.phase-timeline {
    position: relative;
}

.phase-timeline::before {
    content: '';
    position: absolute;
    left: 40px;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(to bottom, #667eea, #764ba2);
    border-radius: 2px;
}

/* Roadmap Milestones */
.roadmap-milestone {
    display: flex;
    align-items: flex-start;
    gap: 30px;
    margin-bottom: 40px;
    position: relative;
}

.milestone-marker {
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 2;
}

.milestone-number {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.milestone-content {
    flex: 1;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.milestone-content:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
}

.milestone-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 10px;
}

.milestone-header h4 {
    color: #333;
    font-size: 1.3rem;
    margin: 0;
    flex: 1;
}

.milestone-date {
    color: #667eea;
    font-weight: 600;
    font-size: 0.9rem;
}

.milestone-status {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.milestone-status.current {
    background: linear-gradient(45deg, #2ecc71, #27ae60);
    color: white;
}

.milestone-status.planned {
    background: linear-gradient(45deg, #3498db, #2980b9);
    color: white;
}

.milestone-description {
    color: #555;
    line-height: 1.6;
    margin-bottom: 20px;
}

/* Milestone Goals */
.milestone-goals {
    margin-bottom: 20px;
}

.milestone-goals h5 {
    color: #333;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1rem;
}

.milestone-goals ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.milestone-goals li {
    padding: 8px 0;
    color: #555;
    position: relative;
    padding-left: 20px;
}

.milestone-goals li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #2ecc71;
    font-weight: bold;
}

/* Milestone Skills */
.milestone-skills {
    margin-bottom: 20px;
}

.milestone-skills h5 {
    color: #333;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1rem;
}

.skills-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

/* Milestone Metrics */
.milestone-metrics {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 20px;
}

/* ARIA Support */
.aria-support {
    background: rgba(102, 126, 234, 0.05);
    border-left: 4px solid #667eea;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.aria-support h5 {
    color: #667eea;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1rem;
}

.aria-support p {
    color: #555;
    line-height: 1.5;
    margin: 0;
}

/* Milestone Actions */
.milestone-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.milestone-btn {
    flex: 1;
    min-width: 120px;
    padding: 12px 16px;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.milestone-btn.primary {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
}

.milestone-btn.secondary {
    background: linear-gradient(45deg, #2ecc71, #27ae60);
    color: white;
}

.milestone-btn.tertiary {
    background: linear-gradient(45deg, #f39c12, #e67e22);
    color: white;
}

.milestone-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Roadmap Summary */
.roadmap-summary {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    border-radius: 20px;
    padding: 40px;
    text-align: center;
}

.roadmap-summary h3 {
    color: #333;
    font-size: 1.8rem;
    margin-bottom: 30px;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.summary-item {
    display: flex;
    align-items: center;
    gap: 20px;
    background: rgba(255, 255, 255, 0.8);
    padding: 25px;
    border-radius: 15px;
    transition: all 0.3s ease;
}

.summary-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

.summary-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(45deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.summary-content {
    text-align: left;
}

.summary-content h4 {
    color: #333;
    margin-bottom: 5px;
    font-size: 1.1rem;
}

.summary-content p {
    color: #666;
    margin: 0;
    font-size: 0.9rem;
}

/* Responsive Design for New Sections */
@media (max-width: 768px) {
    .networking-card,
    .milestone-content {
        padding: 20px;
    }

    .event-header {
        flex-direction: column;
        gap: 15px;
    }

    .event-date {
        align-self: flex-start;
    }

    .event-details {
        grid-template-columns: 1fr;
    }

    .event-actions,
    .milestone-actions {
        flex-direction: column;
    }

    .event-btn,
    .milestone-btn {
        min-width: auto;
    }

    .roadmap-milestone {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .phase-timeline::before {
        display: none;
    }

    .milestone-header {
        flex-direction: column;
        text-align: center;
    }

    .milestone-metrics {
        grid-template-columns: 1fr;
    }

    .summary-grid {
        grid-template-columns: 1fr;
    }

    .summary-item {
        flex-direction: column;
        text-align: center;
    }
}

/* Step 5: Advanced Features Styles */

/* Advanced Features Panel */
.features-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-top: 15px;
}

.feature-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 15px;
    background: linear-gradient(45deg, rgba(74, 144, 226, 0.1), rgba(102, 126, 234, 0.1));
    border: 2px solid rgba(74, 144, 226, 0.2);
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    color: #4a90e2;
    text-decoration: none;
}

.feature-btn:hover {
    background: linear-gradient(45deg, rgba(74, 144, 226, 0.2), rgba(102, 126, 234, 0.2));
    border-color: #4a90e2;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(74, 144, 226, 0.3);
}

.feature-btn i {
    font-size: 1.5rem;
    color: #4a90e2;
}

.feature-btn span {
    font-size: 0.9rem;
    text-align: center;
    line-height: 1.2;
}

/* Search Panel */
.search-container {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    margin-bottom: 10px;
}

.search-input {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid rgba(74, 144, 226, 0.2);
    border-radius: 25px;
    font-size: 0.9rem;
    background: rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: #4a90e2;
    background: white;
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.search-input::placeholder {
    color: #999;
    font-style: italic;
}

.search-btn {
    padding: 12px 16px;
    background: linear-gradient(45deg, #4a90e2, #667eea);
    color: white;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 50px;
}

.search-btn:hover {
    background: linear-gradient(45deg, #357abd, #5a6fd8);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(74, 144, 226, 0.4);
}

.search-shortcuts {
    margin-top: 10px;
}

.shortcut-hint {
    font-size: 0.8rem;
    color: #666;
    font-style: italic;
    background: rgba(74, 144, 226, 0.05);
    padding: 6px 12px;
    border-radius: 12px;
    display: inline-block;
}

/* Enhanced Notification Styles */
.notification {
    position: fixed;
    top: 90px;
    right: 20px;
    background: white;
    padding: 15px 20px;
    border-radius: 12px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    gap: 12px;
    z-index: 10000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    max-width: 350px;
    border-left: 4px solid;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-left-color: #2ecc71;
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.05), rgba(39, 174, 96, 0.05));
}

.notification.success i {
    color: #2ecc71;
}

.notification.info {
    border-left-color: #4a90e2;
    background: linear-gradient(135deg, rgba(74, 144, 226, 0.05), rgba(102, 126, 234, 0.05));
}

.notification.info i {
    color: #4a90e2;
}

.notification.error {
    border-left-color: #e74c3c;
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.05), rgba(192, 57, 43, 0.05));
}

.notification.error i {
    color: #e74c3c;
}

.notification.warning {
    border-left-color: #f39c12;
    background: linear-gradient(135deg, rgba(243, 156, 18, 0.05), rgba(230, 126, 34, 0.05));
}

.notification.warning i {
    color: #f39c12;
}

.notification i {
    font-size: 1.3rem;
    flex-shrink: 0;
}

.notification span {
    color: #333;
    font-weight: 500;
    line-height: 1.4;
}

/* Enhanced Voice Modal Styles */
.voice-modal .modal-content {
    max-width: 500px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 249, 250, 0.95));
    backdrop-filter: blur(20px);
}

.voice-interface {
    text-align: center;
    padding: 20px;
}

.voice-animation {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 12px;
    margin: 30px 0;
    height: 60px;
}

.voice-circle {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background: linear-gradient(45deg, #4a90e2, #667eea);
    animation: voicePulse 1.5s ease-in-out infinite;
    box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
}

.voice-circle:nth-child(2) {
    animation-delay: 0.2s;
}

.voice-circle:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes voicePulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.7;
    }
    50% {
        transform: scale(1.8);
        opacity: 1;
    }
}

#voiceStatus {
    color: #555;
    margin-bottom: 30px;
    font-size: 1.1rem;
    font-weight: 500;
}

.voice-controls {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 30px;
}

.voice-control-btn {
    background: linear-gradient(45deg, #4a90e2, #667eea);
    color: white;
    border: none;
    padding: 15px 25px;
    border-radius: 30px;
    cursor: pointer;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
}

.voice-control-btn:hover:not(:disabled) {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(74, 144, 226, 0.4);
    background: linear-gradient(45deg, #357abd, #5a6fd8);
}

.voice-control-btn:disabled {
    background: linear-gradient(45deg, #bdc3c7, #95a5a6);
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.voice-transcript {
    background: linear-gradient(135deg, rgba(74, 144, 226, 0.05), rgba(102, 126, 234, 0.05));
    border: 2px solid rgba(74, 144, 226, 0.1);
    border-radius: 15px;
    padding: 20px;
    min-height: 100px;
    text-align: left;
    color: #333;
    font-style: italic;
    line-height: 1.6;
    font-size: 1rem;
}

.voice-transcript:empty::before {
    content: 'Transkrypcja głosu pojawi się tutaj...';
    color: #999;
    font-style: italic;
}

/* Enhanced Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(5px);
    animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        backdrop-filter: blur(0px);
    }
    to {
        opacity: 1;
        backdrop-filter: blur(5px);
    }
}

.modal-content {
    background: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 20px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease;
    overflow: hidden;
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.modal-header {
    background: linear-gradient(135deg, #4a90e2, #667eea);
    color: white;
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.modal-header .close {
    color: white;
    font-size: 1.5rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.modal-header .close:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.modal-body {
    padding: 30px;
}

.modal-footer {
    padding: 20px 30px;
    background: #f8f9fa;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
}

.btn-primary,
.btn-secondary {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(45deg, #4a90e2, #667eea);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #357abd, #5a6fd8);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(74, 144, 226, 0.4);
}

.btn-secondary {
    background: #e9ecef;
    color: #495057;
}

.btn-secondary:hover {
    background: #dee2e6;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* Responsive Design for Advanced Features */
@media (max-width: 768px) {
    .features-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .feature-btn {
        padding: 12px;
        flex-direction: row;
        justify-content: flex-start;
        gap: 12px;
    }

    .feature-btn i {
        font-size: 1.2rem;
    }

    .search-container {
        flex-direction: column;
        gap: 10px;
    }

    .search-btn {
        align-self: stretch;
    }

    .notification {
        right: 10px;
        left: 10px;
        max-width: none;
        margin: 0 10px;
    }

    .voice-controls {
        flex-direction: column;
        gap: 15px;
    }

    .voice-control-btn {
        justify-content: center;
    }

    .modal-content {
        width: 95%;
        margin: 10% auto;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 20px;
    }

    .modal-footer {
        flex-direction: column;
    }
}

/* Industry 4.0 Section Styles */
.industry40-grid {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.industry40-overview {
    text-align: center;
    margin-bottom: 30px;
}

.industry40-overview h2 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    background: linear-gradient(45deg, #4a90e2, #667eea);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.specializations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 25px;
}

.specialization-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 25px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.specialization-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(45deg, #4a90e2, #667eea);
}

.specialization-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(74, 144, 226, 0.3);
}

.spec-header {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 20px;
}

.spec-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, #4a90e2, #667eea);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    flex-shrink: 0;
}

.spec-info {
    flex: 1;
}

.spec-info h3 {
    font-size: 1.3rem;
    margin-bottom: 8px;
    color: #333;
}

.spec-info p {
    color: #666;
    font-size: 0.9rem;
}

.spec-relevance {
    text-align: center;
    background: rgba(74, 144, 226, 0.1);
    border-radius: 10px;
    padding: 10px;
    min-width: 80px;
}

.relevance-score {
    display: block;
    font-size: 1.5rem;
    font-weight: bold;
    color: #4a90e2;
}

.relevance-label {
    font-size: 0.8rem;
    color: #666;
}

.spec-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.metric {
    background: rgba(74, 144, 226, 0.05);
    padding: 12px;
    border-radius: 10px;
    text-align: center;
}

.metric-label {
    display: block;
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 5px;
}

.metric-value {
    font-weight: bold;
    color: #333;
}

.metric-value.positive {
    color: #4CAF50;
}

.spec-skills, .spec-projects, .spec-companies {
    margin-bottom: 20px;
}

.spec-skills h4, .spec-projects h4, .spec-companies h4 {
    font-size: 1rem;
    margin-bottom: 10px;
    color: #333;
}

.skills-tags, .companies-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.skill-tag, .company-tag {
    background: rgba(74, 144, 226, 0.1);
    color: #4a90e2;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    border: 1px solid rgba(74, 144, 226, 0.2);
}

.spec-projects ul {
    list-style: none;
    padding: 0;
}

.spec-projects li {
    background: rgba(74, 144, 226, 0.05);
    padding: 8px 12px;
    margin-bottom: 5px;
    border-radius: 8px;
    border-left: 3px solid #4a90e2;
}

.spec-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.spec-actions .btn-primary, .spec-actions .btn-secondary {
    flex: 1;
    padding: 12px;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-weight: 500;
}

.spec-actions .btn-primary {
    background: linear-gradient(45deg, #4a90e2, #667eea);
    color: white;
}

.spec-actions .btn-secondary {
    background: rgba(74, 144, 226, 0.1);
    color: #4a90e2;
    border: 1px solid rgba(74, 144, 226, 0.2);
}

.spec-actions .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(74, 144, 226, 0.4);
}

.spec-actions .btn-secondary:hover {
    background: rgba(74, 144, 226, 0.2);
}

.industry-trends {
    margin-top: 40px;
}

.industry-trends h3 {
    font-size: 1.8rem;
    margin-bottom: 20px;
    text-align: center;
    color: #333;
}

.trends-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.trend-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.trend-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(74, 144, 226, 0.2);
}

.trend-card h4 {
    font-size: 1.1rem;
    margin-bottom: 15px;
    color: #333;
}

.trend-metrics {
    display: flex;
    justify-content: space-between;
    gap: 15px;
}

.trend-metric {
    flex: 1;
}

.trend-label {
    display: block;
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 5px;
}

.trend-value {
    font-weight: bold;
    color: #333;
}

.trend-value.positive {
    color: #4CAF50;
}

/* Market Intelligence Section Styles */
.market-intel-grid {
    display: flex;
    flex-direction: column;
    gap: 40px;
}

.market-overview {
    text-align: center;
    margin-bottom: 30px;
}

.market-overview h2 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    background: linear-gradient(45deg, #4a90e2, #667eea);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.poland-cities h3, .automotive-market h3, .skills-demand h3, .recommendations h3 {
    font-size: 1.8rem;
    margin-bottom: 25px;
    color: #333;
    border-bottom: 2px solid rgba(74, 144, 226, 0.3);
    padding-bottom: 10px;
}

.cities-grid, .automotive-grid, .skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
}

.city-card, .automotive-card, .skill-demand-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 25px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    position: relative;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.city-card.recommended, .automotive-card.current-company {
    border: 2px solid #4a90e2;
    box-shadow: 0 0 20px rgba(74, 144, 226, 0.3);
}

.city-card:hover, .automotive-card:hover, .skill-demand-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(74, 144, 226, 0.3);
}

.city-header, .company-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.city-header h4, .company-header h4 {
    font-size: 1.4rem;
    color: #333;
    margin: 0;
}

.recommended-badge, .current-badge {
    background: linear-gradient(45deg, #4a90e2, #667eea);
    color: white;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.7rem;
    font-weight: bold;
}

.location {
    font-size: 0.9rem;
    color: #666;
}

.city-metrics, .company-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.salary-breakdown, .company-focus, .company-opportunity {
    margin-bottom: 20px;
}

.salary-breakdown h5, .company-focus h5, .company-opportunity h5, .city-companies h5, .city-advantages h5 {
    font-size: 1rem;
    color: #333;
    margin-bottom: 10px;
}

.salary-levels {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.salary-level {
    display: flex;
    justify-content: space-between;
    padding: 8px 12px;
    background: rgba(74, 144, 226, 0.05);
    border-radius: 8px;
}

.companies-list, .focus-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.company-tag, .focus-tag {
    background: rgba(74, 144, 226, 0.1);
    color: #4a90e2;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    border: 1px solid rgba(74, 144, 226, 0.2);
}

.city-advantages ul {
    list-style: none;
    padding: 0;
}

.city-advantages li {
    background: rgba(76, 175, 80, 0.05);
    padding: 8px 12px;
    margin-bottom: 5px;
    border-radius: 8px;
    border-left: 3px solid #4CAF50;
}

.skill-demand-card h4 {
    font-size: 1.2rem;
    color: #333;
    margin-bottom: 15px;
    text-align: center;
}

.skill-metrics {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
}

.skill-metric {
    text-align: center;
    background: rgba(74, 144, 226, 0.05);
    padding: 12px;
    border-radius: 10px;
}

.recommendations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 25px;
}

.recommendation-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 25px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.recommendation-card.immediate {
    border-left: 4px solid #FF6B6B;
}

.recommendation-card.longterm {
    border-left: 4px solid #4a90e2;
}

.recommendation-card h4 {
    font-size: 1.2rem;
    color: #333;
    margin-bottom: 15px;
}

.recommendation-card ul {
    list-style: none;
    padding: 0;
}

.recommendation-card li {
    background: rgba(74, 144, 226, 0.05);
    padding: 12px 15px;
    margin-bottom: 8px;
    border-radius: 10px;
    border-left: 3px solid #4a90e2;
    transition: all 0.3s ease;
}

.recommendation-card.immediate li {
    border-left-color: #FF6B6B;
}

.recommendation-card li:hover {
    background: rgba(74, 144, 226, 0.1);
    transform: translateX(5px);
}

/* Responsive Design for New Sections */
@media (max-width: 768px) {
    .specializations-grid, .cities-grid, .automotive-grid, .skills-grid, .recommendations-grid {
        grid-template-columns: 1fr;
    }

    .specialization-card, .city-card, .automotive-card, .skill-demand-card, .recommendation-card {
        padding: 20px;
    }

    .spec-header, .city-header, .company-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .spec-metrics, .city-metrics, .company-metrics, .skill-metrics {
        grid-template-columns: 1fr;
    }

    .spec-actions {
        flex-direction: column;
    }

    .trends-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .trend-metrics {
        flex-direction: column;
        gap: 10px;
    }
}

/* Enhanced Coursera Section Styles */
.courses-overview {
    text-align: center;
    margin-bottom: 40px;
}

.courses-overview h2 {
    font-size: 2.5rem;
    margin-bottom: 15px;
    background: linear-gradient(45deg, #4a90e2, #667eea);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.trends-highlight {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 25px;
    margin: 20px 0;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.trends-highlight h3 {
    color: #333;
    margin-bottom: 20px;
}

.trends-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.trend-item {
    background: linear-gradient(45deg, #4a90e2, #667eea);
    color: white;
    padding: 20px;
    border-radius: 15px;
    text-align: center;
    transition: all 0.3s ease;
}

.trend-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(74, 144, 226, 0.4);
}

.trend-item h4 {
    font-size: 1.1rem;
    margin-bottom: 10px;
}

.trend-growth {
    font-size: 1.2rem;
    font-weight: bold;
    color: #4CAF50;
    margin-bottom: 5px;
}

.trend-relevance {
    font-size: 0.9rem;
    opacity: 0.9;
}

.courses-grid-enhanced {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 30px;
    margin: 40px 0;
}

.course-card-enhanced {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.course-card-enhanced.trending {
    border: 2px solid #FF6B6B;
    box-shadow: 0 0 20px rgba(255, 107, 107, 0.3);
}

.course-card-enhanced.new {
    border: 2px solid #4CAF50;
    box-shadow: 0 0 20px rgba(76, 175, 80, 0.3);
}

.course-card-enhanced:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

.trending-badge, .new-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
    z-index: 2;
}

.trending-badge {
    background: linear-gradient(45deg, #FF6B6B, #FF8E53);
    color: white;
}

.new-badge {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
}

.course-header-enhanced {
    margin-bottom: 20px;
}

.course-header-enhanced h3 {
    font-size: 1.4rem;
    color: #333;
    margin-bottom: 10px;
    line-height: 1.3;
}

.course-provider {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.provider-name {
    font-weight: 600;
    color: #4a90e2;
}

.course-rating {
    display: flex;
    align-items: center;
    gap: 5px;
}

.course-rating i {
    color: #FFD700;
}

.enrollments {
    font-size: 0.8rem;
    color: #666;
}

.course-metrics-enhanced {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-bottom: 20px;
}

.metric-item {
    background: rgba(74, 144, 226, 0.05);
    padding: 12px;
    border-radius: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.metric-label {
    font-size: 0.9rem;
    color: #666;
}

.metric-value {
    font-weight: 600;
    color: #333;
}

.metric-value.difficulty-beginner {
    color: #4CAF50;
}

.metric-value.difficulty-intermediate,
.metric-value.difficulty-beginner-to-intermediate {
    color: #FF9800;
}

.metric-value.difficulty-advanced,
.metric-value.difficulty-intermediate-to-advanced {
    color: #F44336;
}

.course-relevance-enhanced {
    margin-bottom: 20px;
}

.relevance-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.relevance-label {
    font-size: 0.9rem;
    color: #666;
}

.relevance-value {
    font-weight: bold;
    color: #4a90e2;
}

.relevance-bar {
    height: 8px;
    background: rgba(74, 144, 226, 0.1);
    border-radius: 4px;
    overflow: hidden;
}

.relevance-fill {
    height: 100%;
    background: linear-gradient(45deg, #4a90e2, #667eea);
    transition: width 0.3s ease;
}

.course-roi {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-bottom: 20px;
}

.roi-item {
    background: rgba(76, 175, 80, 0.05);
    padding: 12px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.roi-item i {
    color: #4CAF50;
}

.roi-label {
    font-size: 0.9rem;
    color: #666;
}

.roi-value {
    font-weight: 600;
    color: #4CAF50;
}

.course-skills-enhanced {
    margin-bottom: 20px;
}

.course-skills-enhanced h4 {
    font-size: 1rem;
    color: #333;
    margin-bottom: 10px;
}

.skills-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.skill-tag-enhanced {
    background: linear-gradient(45deg, #4a90e2, #667eea);
    color: white;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.skill-tag-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(74, 144, 226, 0.4);
}

.manufacturing-relevance-enhanced {
    background: rgba(255, 152, 0, 0.05);
    border-left: 4px solid #FF9800;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.manufacturing-relevance-enhanced h4 {
    color: #FF9800;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.manufacturing-relevance-enhanced p {
    color: #666;
    margin: 0;
    line-height: 1.5;
}

.course-actions-enhanced {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.course-btn-enhanced {
    flex: 1;
    padding: 12px 16px;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-weight: 500;
    font-size: 0.9rem;
}

.course-btn-enhanced.primary {
    background: linear-gradient(45deg, #4a90e2, #667eea);
    color: white;
}

.course-btn-enhanced.secondary {
    background: rgba(74, 144, 226, 0.1);
    color: #4a90e2;
    border: 1px solid rgba(74, 144, 226, 0.2);
}

.course-btn-enhanced.tertiary {
    background: rgba(76, 175, 80, 0.1);
    color: #4CAF50;
    border: 1px solid rgba(76, 175, 80, 0.2);
}

.course-btn-enhanced:hover {
    transform: translateY(-2px);
}

.course-btn-enhanced.primary:hover {
    box-shadow: 0 5px 15px rgba(74, 144, 226, 0.4);
}

.course-btn-enhanced.secondary:hover {
    background: rgba(74, 144, 226, 0.2);
}

.course-btn-enhanced.tertiary:hover {
    background: rgba(76, 175, 80, 0.2);
}

.platform-comparison {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 30px;
    margin: 40px 0;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.platform-comparison h3 {
    color: #333;
    margin-bottom: 25px;
    text-align: center;
}

.comparison-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.platform-card {
    background: rgba(74, 144, 226, 0.05);
    border-radius: 15px;
    padding: 20px;
    border: 1px solid rgba(74, 144, 226, 0.1);
    transition: all 0.3s ease;
}

.platform-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(74, 144, 226, 0.2);
}

.platform-card h4 {
    color: #4a90e2;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.platform-rating {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-bottom: 10px;
}

.platform-rating i {
    color: #FFD700;
}

.platform-courses {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 15px;
}

.platform-best-for {
    margin-bottom: 15px;
    font-size: 0.9rem;
}

.platform-strengths ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.platform-strengths li {
    background: rgba(76, 175, 80, 0.05);
    padding: 5px 10px;
    margin-bottom: 5px;
    border-radius: 5px;
    border-left: 3px solid #4CAF50;
    font-size: 0.8rem;
}

/* Responsive Design for Enhanced Courses */
@media (max-width: 768px) {
    .courses-grid-enhanced {
        grid-template-columns: 1fr;
    }

    .course-card-enhanced {
        padding: 20px;
    }

    .course-metrics-enhanced {
        grid-template-columns: 1fr;
    }

    .course-actions-enhanced {
        flex-direction: column;
    }

    .trends-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .comparison-grid {
        grid-template-columns: 1fr;
    }
}
