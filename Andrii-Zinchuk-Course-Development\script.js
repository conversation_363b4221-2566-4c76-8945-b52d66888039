// <PERSON><PERSON><PERSON> Personalized Learning Platform
// Enhanced with Manus AI capabilities

// Profile data based on <PERSON><PERSON><PERSON>'s CV
const and<PERSON><PERSON><PERSON><PERSON><PERSON>le = {
    name: "Dr. <PERSON><PERSON><PERSON>",
    currentRole: "Process Engineer SMT/CBA at Aptiv",
    experience: 15,
    education: "Ph.D. in Metallic Materials Engineering and Ceramics",
    languages: ["Russian", "Ukrainian", "Polish", "English"],
    skills: {
        engineering: {
            "Materials Science": 95,
            "PVD Coating": 90,
            "Surface Engineering": 88,
            "Quality Engineering": 85,
            "Metallurgy": 92,
            "Heat Treatment": 87
        },
        technology: {
            "Python Programming": 70,
            "Data Analysis": 65,
            "AI/ML": 60,
            "SAP MM": 75,
            "RPA": 80,
            "Statistical Analysis": 68
        },
        business: {
            "Project Management": 85,
            "Lean Six Sigma": 80,
            "Business Analysis": 78,
            "Process Improvement": 88,
            "Quality Management": 90
        }
    },
    certifications: [
        "SS&C Blue Prism RPA Professional Certificate",
        "Programming for Everybody (Python)",
        "Learning SAP MM (Materials Management)",
        "The Growth Mindset",
        "Effective Problem-Solving and Decision-Making"
    ],
    interests: ["AI", "Data Science", "Materials Engineering", "Automation", "Business Intelligence"]
};

// Course recommendations based on <PERSON><PERSON><PERSON>'s profile and Manus AI analysis
let courseRecommendations = [
    {
        title: "Advanced Data Science for Engineers",
        provider: "Coursera",
        category: "data-science",
        level: "Intermediate",
        duration: "8 weeks",
        rating: 4.8,
        description: "Apply data science techniques to engineering problems, perfect for materials engineers transitioning to data roles.",
        relevance: 95,
        skills: ["Python", "Data Analysis", "Machine Learning", "Engineering Applications"],
        url: "https://www.coursera.org/specializations/data-science",
        status: "available",
        id: 1
    },
    {
        title: "AI for Manufacturing and Quality Control",
        provider: "edX",
        category: "ai-ml",
        level: "Advanced",
        duration: "10 weeks",
        rating: 4.7,
        description: "Learn how AI transforms manufacturing processes and quality control systems.",
        relevance: 92,
        skills: ["AI", "Quality Control", "Manufacturing", "Process Optimization"],
        url: "https://www.edx.org/course/artificial-intelligence",
        status: "available",
        id: 2
    },
    {
        title: "Business Intelligence for Technical Professionals",
        provider: "Coursera",
        category: "business",
        level: "Intermediate",
        duration: "6 weeks",
        rating: 4.6,
        description: "Bridge technical expertise with business intelligence and strategic decision-making.",
        relevance: 88,
        skills: ["BI Tools", "Data Visualization", "Business Strategy", "Analytics"],
        url: "https://www.coursera.org/specializations/business-intelligence",
        status: "available",
        id: 3
    },
    {
        title: "Advanced Materials Characterization with ML",
        provider: "MIT OpenCourseWare",
        category: "engineering",
        level: "Advanced",
        duration: "12 weeks",
        rating: 4.9,
        description: "Combine materials science expertise with machine learning for advanced characterization.",
        relevance: 94,
        skills: ["Materials Science", "Machine Learning", "Data Analysis", "Research"],
        url: "https://ocw.mit.edu/courses/materials-science-and-engineering/",
        status: "enrolled",
        id: 4
    },
    {
        title: "Python for Data Analysis in Engineering",
        provider: "DataCamp",
        category: "data-science",
        level: "Beginner",
        duration: "4 weeks",
        rating: 4.5,
        description: "Master Python programming for engineering data analysis and visualization.",
        relevance: 85,
        skills: ["Python", "Pandas", "NumPy", "Data Visualization"],
        url: "https://www.datacamp.com/courses/intro-to-python-for-data-science",
        status: "completed",
        id: 5
    },
    {
        title: "Lean Six Sigma for Digital Transformation",
        provider: "Coursera",
        category: "business",
        level: "Intermediate",
        duration: "8 weeks",
        rating: 4.7,
        description: "Apply Lean Six Sigma principles in digital transformation initiatives.",
        relevance: 87,
        skills: ["Lean Six Sigma", "Digital Transformation", "Process Improvement", "Change Management"],
        url: "https://www.coursera.org/specializations/lean-six-sigma",
        status: "in-progress",
        id: 6
    }
];

// Learning roadmap for Andrii's career development
const learningRoadmap = [
    {
        phase: "Foundation",
        title: "Strengthen Data Science Fundamentals",
        duration: "3 months",
        courses: ["Python for Data Analysis", "Statistics for Engineers", "Data Visualization"],
        status: "in-progress"
    },
    {
        phase: "Intermediate",
        title: "AI/ML for Engineering Applications",
        duration: "4 months",
        courses: ["Machine Learning for Engineers", "AI in Manufacturing", "Predictive Analytics"],
        status: "planned"
    },
    {
        phase: "Advanced",
        title: "Leadership in Tech Innovation",
        duration: "6 months",
        courses: ["Technical Leadership", "Innovation Management", "Digital Strategy"],
        status: "future"
    },
    {
        phase: "Specialization",
        title: "Expert in AI-Driven Materials Science",
        duration: "8 months",
        courses: ["Advanced ML for Materials", "Research Methodology", "Industry 4.0"],
        status: "future"
    }
];

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeParticles();
    initializeCharts();
    generateCourseRecommendations();
    populateActivityList();
    createRoadmap();
    setupEventListeners();
});

// Initialize particle background
function initializeParticles() {
    particlesJS('particles-js', {
        particles: {
            number: { value: 80, density: { enable: true, value_area: 800 } },
            color: { value: "#ffffff" },
            shape: { type: "circle" },
            opacity: { value: 0.5, random: false },
            size: { value: 3, random: true },
            line_linked: { enable: true, distance: 150, color: "#ffffff", opacity: 0.4, width: 1 },
            move: { enable: true, speed: 6, direction: "none", random: false, straight: false, out_mode: "out", bounce: false }
        },
        interactivity: {
            detect_on: "canvas",
            events: { onhover: { enable: true, mode: "repulse" }, onclick: { enable: true, mode: "push" }, resize: true },
            modes: { grab: { distance: 400, line_linked: { opacity: 1 } }, bubble: { distance: 400, size: 40, duration: 2, opacity: 8, speed: 3 }, repulse: { distance: 200, duration: 0.4 }, push: { particles_nb: 4 }, remove: { particles_nb: 2 } }
        },
        retina_detect: true
    });
}

// Initialize charts
function initializeCharts() {
    // Progress Chart
    const progressCtx = document.getElementById('progressChart').getContext('2d');
    new Chart(progressCtx, {
        type: 'doughnut',
        data: {
            labels: ['Completed', 'In Progress', 'Planned'],
            datasets: [{
                data: [35, 25, 40],
                backgroundColor: ['#4a90e2', '#ff6b6b', '#f39c12'],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: { position: 'bottom' }
            }
        }
    });

    // Skill Chart
    const skillCtx = document.getElementById('skillChart').getContext('2d');
    new Chart(skillCtx, {
        type: 'radar',
        data: {
            labels: ['Materials Science', 'Data Analysis', 'AI/ML', 'Python', 'Quality Eng.', 'Project Mgmt'],
            datasets: [{
                label: 'Current Level',
                data: [95, 65, 60, 70, 85, 85],
                backgroundColor: 'rgba(74, 144, 226, 0.2)',
                borderColor: '#4a90e2',
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            scales: {
                r: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });

    // Learning Analytics Chart
    const learningCtx = document.getElementById('learningChart').getContext('2d');
    new Chart(learningCtx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [{
                label: 'Learning Hours',
                data: [20, 35, 45, 30, 55, 40],
                borderColor: '#4a90e2',
                backgroundColor: 'rgba(74, 144, 226, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: { display: false }
            }
        }
    });
}

// Generate course recommendations using Manus AI logic
function generateCourseRecommendations() {
    const recommendationsContainer = document.getElementById('recommendations');

    // Simulate AI analysis for recommendations panel
    setTimeout(() => {
        recommendationsContainer.innerHTML = `
            <div class="ai-recommendation">
                <h4><i class="fas fa-robot"></i> Manus AI Analysis</h4>
                <p>Based on your materials engineering background and growing interest in AI/Data Science, I recommend focusing on:</p>
                <ul>
                    <li>Advanced Python programming for engineering applications</li>
                    <li>Machine Learning techniques for materials characterization</li>
                    <li>Business Intelligence to leverage your technical expertise</li>
                </ul>
                <div class="confidence-score">
                    <span>Confidence Score: 94%</span>
                    <div class="score-bar">
                        <div class="score-fill" style="width: 94%"></div>
                    </div>
                </div>
            </div>
        `;
    }, 2000);

    // The course grid is now handled by course-manager.js
    // This ensures compatibility with the new course management system
}

// Populate activity list
function populateActivityList() {
    const activities = [
        { action: "Completed Python Fundamentals", time: "2 hours ago", icon: "fas fa-check-circle", type: "success" },
        { action: "Started AI for Manufacturing course", time: "1 day ago", icon: "fas fa-play", type: "info" },
        { action: "Earned RPA Professional Certificate", time: "3 days ago", icon: "fas fa-certificate", type: "success" },
        { action: "Updated skill assessment", time: "1 week ago", icon: "fas fa-chart-line", type: "info" }
    ];

    const activityList = document.getElementById('activityList');
    activityList.innerHTML = activities.map(activity => `
        <div class="activity-item ${activity.type}">
            <i class="${activity.icon}"></i>
            <div class="activity-content">
                <span class="activity-action">${activity.action}</span>
                <span class="activity-time">${activity.time}</span>
            </div>
        </div>
    `).join('');
}

// Create learning roadmap
function createRoadmap() {
    const roadmapPath = document.getElementById('roadmapPath');
    roadmapPath.innerHTML = learningRoadmap.map((phase, index) => `
        <div class="roadmap-item ${phase.status}">
            <div class="roadmap-marker">${index + 1}</div>
            <div class="roadmap-content">
                <h3>${phase.title}</h3>
                <p class="roadmap-phase">${phase.phase} Phase</p>
                <p class="roadmap-duration">Duration: ${phase.duration}</p>
                <div class="roadmap-courses">
                    ${phase.courses.map(course => `<span class="course-tag">${course}</span>`).join('')}
                </div>
                <div class="roadmap-status ${phase.status}">
                    ${phase.status === 'in-progress' ? 'In Progress' :
                      phase.status === 'planned' ? 'Planned' : 'Future'}
                </div>
            </div>
        </div>
    `).join('');
}

// Setup event listeners
function setupEventListeners() {
    // Course filter buttons
    const filterBtns = document.querySelectorAll('.filter-btn');
    filterBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            filterBtns.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
            filterCourses(btn.dataset.filter);
        });
    });

    // Mobile navigation
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');

    if (hamburger) {
        hamburger.addEventListener('click', () => {
            hamburger.classList.toggle('active');
            navMenu.classList.toggle('active');
        });
    }

    // Smooth scrolling for navigation links
    document.querySelectorAll('.nav-link').forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const targetId = link.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            if (targetSection) {
                targetSection.scrollIntoView({ behavior: 'smooth' });
            }
        });
    });
}

// Filter courses by category
function filterCourses(category) {
    const courseCards = document.querySelectorAll('.course-card');
    courseCards.forEach(card => {
        if (category === 'all' || card.dataset.category === category) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

// Legacy enroll function for compatibility
function enrollCourse(courseTitle, courseIndex = null) {
    // If courseIndex is provided, use the new course management system
    if (courseIndex !== null && window.enrollCourse) {
        return window.enrollCourse(courseTitle, courseIndex);
    }

    // Fallback for legacy calls
    showNotification(`Enrolling in "${courseTitle}"...`, 'info');

    // Add to activity list
    const activityList = document.getElementById('activityList');
    if (activityList) {
        const newActivity = document.createElement('div');
        newActivity.className = 'activity-item info';
        newActivity.innerHTML = `
            <i class="fas fa-play"></i>
            <div class="activity-content">
                <span class="activity-action">Enrolled in ${courseTitle}</span>
                <span class="activity-time">Just now</span>
            </div>
        `;
        activityList.insertBefore(newActivity, activityList.firstChild);
    }
}

// Generate courses using AI (main function called from HTML)
function generateCourses() {
    // Simulate AI course generation
    const loadingMessage = document.createElement('div');
    loadingMessage.className = 'ai-loading';
    loadingMessage.innerHTML = `
        <div class="loading-animation">
            <i class="fas fa-robot fa-spin"></i>
            <p>Manus AI is analyzing your profile and generating personalized courses...</p>
        </div>
    `;

    document.body.appendChild(loadingMessage);

    setTimeout(() => {
        document.body.removeChild(loadingMessage);
        document.getElementById('courses').scrollIntoView({ behavior: 'smooth' });

        // Show success message
        const successMessage = document.createElement('div');
        successMessage.className = 'success-notification';
        successMessage.innerHTML = `
            <i class="fas fa-check-circle"></i>
            <span>AI analysis complete! Personalized courses generated based on your profile.</span>
        `;
        document.body.appendChild(successMessage);

        setTimeout(() => {
            document.body.removeChild(successMessage);
        }, 3000);
    }, 3000);
}

// Add CSS for new elements
const additionalStyles = `
    .course-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
        margin-bottom: 20px;
    }

    .course-card:hover {
        transform: translateY(-5px);
    }

    .course-header h3 {
        color: #333;
        margin-bottom: 10px;
        font-size: 1.3rem;
    }

    .course-provider {
        color: #4a90e2;
        font-weight: 600;
        font-size: 0.9rem;
    }

    .course-meta {
        display: flex;
        gap: 15px;
        margin: 15px 0;
        flex-wrap: wrap;
    }

    .level {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .level.beginner { background: #e8f5e8; color: #2e7d32; }
    .level.intermediate { background: #fff3e0; color: #f57c00; }
    .level.advanced { background: #ffebee; color: #c62828; }

    .duration, .rating {
        color: #666;
        font-size: 0.9rem;
    }

    .course-description {
        color: #555;
        line-height: 1.6;
        margin: 15px 0;
    }

    .course-skills {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin: 15px 0;
    }

    .course-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 20px;
    }

    .relevance-score {
        flex: 1;
    }

    .relevance-bar, .score-bar {
        background: #e0e0e0;
        height: 6px;
        border-radius: 3px;
        overflow: hidden;
        margin-top: 5px;
    }

    .relevance-fill, .score-fill {
        height: 100%;
        background: linear-gradient(45deg, #4a90e2, #357abd);
        border-radius: 3px;
    }

    .enroll-btn {
        background: linear-gradient(45deg, #4a90e2, #357abd);
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 25px;
        cursor: pointer;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .enroll-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(74, 144, 226, 0.4);
    }

    .ai-recommendation {
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
        padding: 20px;
        border-radius: 10px;
    }

    .ai-recommendation h4 {
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .ai-recommendation ul {
        margin: 15px 0;
        padding-left: 20px;
    }

    .confidence-score {
        margin-top: 15px;
    }

    .activity-item {
        display: flex;
        align-items: center;
        gap: 15px;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 10px;
    }

    .activity-item.success {
        background: rgba(76, 175, 80, 0.1);
        color: #2e7d32;
    }

    .activity-item.info {
        background: rgba(74, 144, 226, 0.1);
        color: #1976d2;
    }

    .activity-content {
        display: flex;
        flex-direction: column;
    }

    .activity-action {
        font-weight: 600;
        margin-bottom: 5px;
    }

    .activity-time {
        font-size: 0.8rem;
        opacity: 0.7;
    }

    .roadmap-item {
        display: flex;
        gap: 20px;
        margin-bottom: 30px;
        position: relative;
    }

    .roadmap-item:not(:last-child)::after {
        content: '';
        position: absolute;
        left: 20px;
        top: 60px;
        width: 2px;
        height: 50px;
        background: #ddd;
    }

    .roadmap-marker {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: #4a90e2;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        flex-shrink: 0;
    }

    .roadmap-content {
        background: rgba(255, 255, 255, 0.95);
        padding: 20px;
        border-radius: 10px;
        flex: 1;
    }

    .roadmap-content h3 {
        color: #333;
        margin-bottom: 10px;
    }

    .roadmap-phase {
        color: #4a90e2;
        font-weight: 600;
        margin-bottom: 5px;
    }

    .roadmap-duration {
        color: #666;
        margin-bottom: 15px;
    }

    .roadmap-courses {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-bottom: 15px;
    }

    .course-tag {
        background: rgba(74, 144, 226, 0.1);
        color: #4a90e2;
        padding: 4px 10px;
        border-radius: 15px;
        font-size: 0.8rem;
    }

    .roadmap-status {
        padding: 5px 15px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        display: inline-block;
    }

    .roadmap-status.in-progress {
        background: #fff3e0;
        color: #f57c00;
    }

    .roadmap-status.planned {
        background: #e3f2fd;
        color: #1976d2;
    }

    .roadmap-status.future {
        background: #f3e5f5;
        color: #7b1fa2;
    }

    .ai-loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        color: white;
    }

    .loading-animation {
        text-align: center;
    }

    .loading-animation i {
        font-size: 3rem;
        margin-bottom: 20px;
        color: #4a90e2;
    }

    .success-notification {
        position: fixed;
        top: 20px;
        right: 20px;
        background: #4caf50;
        color: white;
        padding: 15px 20px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        gap: 10px;
        z-index: 10000;
        animation: slideIn 0.3s ease;
    }

    @keyframes slideIn {
        from { transform: translateX(100%); }
        to { transform: translateX(0); }
    }
`;

// Add the additional styles to the page
const styleSheet = document.createElement('style');
styleSheet.textContent = additionalStyles;
document.head.appendChild(styleSheet);
