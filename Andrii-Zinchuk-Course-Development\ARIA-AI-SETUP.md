# 🤖 ARIA AI Coach - Popup Interface - Konfiguracja i Instrukcja Obsługi

## 🎯 Przegląd Funkcjonalności

ARIA AI Coach to zaawansowany asystent kariery z eleganckim popup interface:
- **Popup Window** - Wyskakujące okienko na żądanie
- **Floating Button** - Zawsze widoczny przycisk dostępu
- **Integracji z AI** - Grok, Google Gemini, OpenAI, Claude
- **Quick Replies** - Szybkie odpowiedzi dla częstych pytań
- **Notyfikacje** - Powiadomienia o nowych wiadomościach
- **Personalizacji** - Dostosowanie do profilu Andrii <PERSON>
- **Bezpieczeństwa** - Lokalne przechowywanie kluczy API

## 🚀 Szybki Start

### 1. Otwórz ARIA Coach
```
Otwórz plik: aria-coach.html w przeglądarce
```

### 2. Konfiguracja AI (Pierwsza konfiguracja)
1. Klik<PERSON><PERSON> ikonę **⚙️** w prawym górnym rogu ARIA
2. Wybierz dostawcę AI (zalecane: **Grok**)
3. Wprowadź swój API Key
4. Kliknij **"Test Połączenia"**
5. Zapisz ustawienia

## 🔧 Konfiguracja API Keys

### Grok (xAI) - Zalecane ⭐
```
1. Idź na: https://console.x.ai/
2. Zarejestruj się / Zaloguj
3. Przejdź do API Keys
4. Stwórz nowy klucz
5. Skopiuj i wklej do ARIA
```

### Google Gemini
```
1. Idź na: https://makersuite.google.com/app/apikey
2. Stwórz API Key
3. Skopiuj klucz
4. W ARIA wybierz "Google Gemini"
5. Wklej klucz
```

### OpenAI
```
1. Idź na: https://platform.openai.com/api-keys
2. Stwórz nowy klucz
3. Skopiuj klucz
4. W ARIA wybierz "OpenAI GPT-4"
5. Wklej klucz
```

## 🎮 Jak Używać ARIA

### Otwieranie/Zamykanie Popup
- **Kliknij floating button** 🤖 - Otwiera popup ARIA
- **Kliknij X** - Zamyka popup
- **Kliknij poza popup** - Automatycznie zamyka
- **Naciśnij Escape** - Zamyka popup
- **Czerwona kropka** - Pokazuje nowe wiadomości

### Chat z AI
1. **Otwórz popup** - Kliknij floating button 🤖
2. **Quick Replies** - Kliknij gotowe pytania lub
3. **Wpisz wiadomość** w polu tekstowym
4. **Wyślij** - Naciśnij **Enter** lub kliknij **📤**
5. **ARIA odpowie** używając skonfigurowanego AI
6. **Notyfikacje** - Czerwona kropka przy nowych wiadomościach

### Przykładowe Pytania
```
"Jakie kursy Coursera polecasz dla transformacji z Manufacturing do AI?"
"Jak mogę wykorzystać moje doświadczenie w SMT w Data Science?"
"Jakie są najlepsze certyfikacje AI dla automotive industry?"
"Pomóż mi zaplanować ścieżkę kariery na następne 12 miesięcy"
```

## ⚙️ Zaawansowane Ustawienia

### Parametry AI
- **Temperature (0-1)**: Kreatywność odpowiedzi (0.7 zalecane)
- **Max Tokens**: Długość odpowiedzi (2000 zalecane)
- **System Prompt**: Instrukcje dla AI (predefiniowane dla Andrii)

### Personalizacja
- **Profil**: Andrii Zinchuk (Manufacturing Engineer)
- **Styl**: Professional/Friendly/Technical/Motivational
- **Język**: Polski/English/Mixed

### Bezpieczeństwo
- **Szyfrowanie**: Lokalne szyfrowanie danych ✅
- **Logowanie**: Opcjonalne zapisywanie konwersacji
- **Klucze API**: Przechowywane tylko lokalnie

## 🔍 Rozwiązywanie Problemów

### Błąd: "Brak klucza API"
```
Rozwiązanie:
1. Kliknij ⚙️ w ARIA
2. Wprowadź poprawny API Key
3. Kliknij "Test Połączenia"
4. Zapisz ustawienia
```

### Błąd: "Connection failed"
```
Możliwe przyczyny:
- Nieprawidłowy API Key
- Brak środków na koncie API
- Problemy z internetem
- Nieprawidłowy endpoint

Rozwiązanie:
1. Sprawdź API Key
2. Sprawdź saldo konta
3. Spróbuj innego dostawcy AI
```

### ARIA nie odpowiada
```
Rozwiązanie:
1. Sprawdź status połączenia (⚙️ → Status Połączenia)
2. Kliknij "Test Połączenia"
3. Sprawdź konsolę przeglądarki (F12)
4. Zrestartuj przeglądarkę
```

## 🎯 Funkcje Specjalne

### Kontekst Świadomy
ARIA automatycznie rozpoznaje:
- Aktualną sekcję platformy
- Profil użytkownika (Andrii Zinchuk)
- Background (Manufacturing → AI transition)
- Lokalizację (Gdańsk, Polska)

### Manus AI Integration
ARIA wykorzystuje logikę Manus AI dla:
- Personalized insights
- Deep analysis
- Career recommendations
- Industry-specific advice

### Floating Notifications
- Czerwona kropka pokazuje nowe wiadomości
- Kliknij floating button aby przeczytać

## 📱 Responsywność

ARIA działa na:
- **Desktop** - Pełna funkcjonalność
- **Tablet** - Adaptacyjny interfejs
- **Mobile** - Zoptymalizowany floating button

## 🔄 Aktualizacje

### Automatyczne Zapisywanie
- Ustawienia AI - automatycznie
- Historia konwersacji - opcjonalnie
- Preferencje użytkownika - lokalnie

### Backup/Restore
```
Export: ⚙️ → Zaawansowane → Export Danych
Import: ⚙️ → Zaawansowane → Import Danych
```

## 🎓 Wskazówki dla Andrii

### Optymalne Wykorzystanie
1. **Rozpocznij od konfiguracji Grok** - najlepsza jakość dla career coaching
2. **Używaj konkretnych pytań** - "Jak przejść z SMT do Computer Vision?"
3. **Wykorzystuj kontekst** - ARIA zna Twój background w manufacturing
4. **Regularnie aktualizuj postęp** - informuj o ukończonych kursach

### Przykładowe Scenariusze
```
Scenario 1: Planowanie kursu
"Mam 3 miesiące na naukę. Który kurs Coursera da mi największy boost w CV?"

Scenario 2: Networking
"Jakie wydarzenia AI w Gdańsku polecasz w tym miesiącu?"

Scenario 3: Portfolio
"Jak mogę pokazać moje doświadczenie z PVD coating w projekcie ML?"
```

## 🚨 Ważne Informacje

### Bezpieczeństwo API Keys
- **NIE UDOSTĘPNIAJ** swoich kluczy API
- Klucze są przechowywane tylko lokalnie
- Regularnie rotuj klucze API

### Koszty API
- Grok: ~$0.01-0.05 per conversation
- Google: Darmowe limity, potem płatne
- OpenAI: ~$0.02-0.10 per conversation

### Prywatność
- Wszystkie dane pozostają lokalne
- Brak wysyłania danych na serwery ARIA
- Konwersacje z AI podlegają polityce dostawcy

---

**Powodzenia w transformacji kariery z Manufacturing do AI! 🚀**

*ARIA jest zawsze gotowa do pomocy w Twojej podróży zawodowej.*
