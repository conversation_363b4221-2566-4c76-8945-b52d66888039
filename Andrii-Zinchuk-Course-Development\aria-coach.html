<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Coach ARIA - <PERSON><PERSON><PERSON> Transformation</title>
    <link rel="stylesheet" href="aria-styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/particles.js/2.0.0/particles.min.js"></script>
</head>
<body>
    <div id="particles-js"></div>

    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-robot"></i>
                <span>AI Coach ARIA</span>
            </div>
            <ul class="nav-menu">
                <li><a href="#dashboard" class="nav-link active">Dashboard</a></li>
                <li><a href="#courses" class="nav-link">Kursy Coursera</a></li>
                <li><a href="#certifications" class="nav-link">Certyfikacje Premium</a></li>
                <li><a href="#portfolio" class="nav-link">Portfolio Platforms</a></li>
                <li><a href="#networking" class="nav-link">Networking Events</a></li>
                <li><a href="#roadmap" class="nav-link">Mapa Kariery</a></li>
                <li><a href="#industry40" class="nav-link">Industry 4.0</a></li>
                <li><a href="#market-intel" class="nav-link">Market Intel</a></li>
                <li><a href="#manus-insights" class="nav-link">Manus AI Insights</a></li>
            </ul>
            <div class="voice-toggle">
                <button id="voiceToggle" class="voice-btn" title="Włącz/Wyłącz Głos">
                    <i class="fas fa-microphone"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-container">
        <!-- Left Content Area -->
        <div class="content-area">

            <!-- Dashboard Section -->
            <section id="dashboard" class="section active">
                <div class="section-header">
                    <h1>Career Transformation Dashboard</h1>
                    <p>Twoja podróż z Manufacturing Engineering do AI/Data Science</p>
                </div>

                <div class="dashboard-grid">
                    <!-- Career Progress Gauge -->
                    <div class="dashboard-card progress-card">
                        <h3><i class="fas fa-chart-line"></i> Postęp Transformacji Kariery</h3>
                        <div class="progress-gauge">
                            <canvas id="progressGauge"></canvas>
                            <div class="gauge-center">
                                <span class="gauge-value">35%</span>
                                <span class="gauge-label">Ukończone</span>
                            </div>
                        </div>
                        <div class="progress-details">
                            <div class="detail-item">
                                <span>Obecna pozycja:</span>
                                <span>Process Engineer SMT/CBA</span>
                            </div>
                            <div class="detail-item">
                                <span>Cel:</span>
                                <span>Senior Data Scientist (Materials)</span>
                            </div>
                            <div class="detail-item">
                                <span>Przewidywany czas:</span>
                                <span>18-24 miesiące</span>
                            </div>
                        </div>
                    </div>

                    <!-- Skills Radar -->
                    <div class="dashboard-card skills-card">
                        <h3><i class="fas fa-brain"></i> Mapa Kompetencji</h3>
                        <canvas id="skillsRadar"></canvas>
                        <div class="skills-legend">
                            <div class="legend-item current">
                                <span class="legend-color"></span>
                                <span>Obecny poziom</span>
                            </div>
                            <div class="legend-item target">
                                <span class="legend-color"></span>
                                <span>Poziom docelowy</span>
                            </div>
                        </div>
                    </div>

                    <!-- Salary Projection -->
                    <div class="dashboard-card salary-card">
                        <h3><i class="fas fa-dollar-sign"></i> Projekcja Wynagrodzeń</h3>
                        <canvas id="salaryChart"></canvas>
                        <div class="salary-details">
                            <div class="salary-current">
                                <span class="label">Obecne:</span>
                                <span class="value">€65,000 - €75,000</span>
                            </div>
                            <div class="salary-target">
                                <span class="label">Cel (2 lata):</span>
                                <span class="value">€120,000 - €200,000</span>
                            </div>
                            <div class="salary-growth">
                                <span class="label">Wzrost:</span>
                                <span class="value positive">+85% - +167%</span>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="dashboard-card actions-card">
                        <h3><i class="fas fa-bolt"></i> Szybkie Akcje</h3>
                        <div class="action-buttons">
                            <button class="action-btn primary" onclick="startCourse()">
                                <i class="fas fa-play"></i>
                                <span>Rozpocznij Kurs</span>
                            </button>
                            <button class="action-btn secondary" onclick="updateProgress()">
                                <i class="fas fa-check"></i>
                                <span>Aktualizuj Postęp</span>
                            </button>
                            <button class="action-btn tertiary" onclick="scheduleNetworking()">
                                <i class="fas fa-users"></i>
                                <span>Zaplanuj Networking</span>
                            </button>
                            <button class="action-btn quaternary" onclick="reviewPortfolio()">
                                <i class="fas fa-folder"></i>
                                <span>Przejrzyj Portfolio</span>
                            </button>
                        </div>
                    </div>

                    <!-- Recent Achievements -->
                    <div class="dashboard-card achievements-card">
                        <h3><i class="fas fa-trophy"></i> Ostatnie Osiągnięcia</h3>
                        <div class="achievements-list" id="achievementsList">
                            <!-- Achievements will be populated by JavaScript -->
                        </div>
                        <button class="view-all-btn" onclick="viewAllAchievements()">
                            Zobacz wszystkie osiągnięcia
                        </button>
                    </div>

                    <!-- Timeline Milestones -->
                    <div class="dashboard-card timeline-card">
                        <h3><i class="fas fa-road"></i> Najbliższe Milestones</h3>
                        <div class="timeline-preview" id="timelinePreview">
                            <!-- Timeline items will be populated by JavaScript -->
                        </div>
                    </div>

                    <!-- Advanced Features Panel -->
                    <div class="dashboard-card advanced-features">
                        <h3><i class="fas fa-cogs"></i> Zaawansowane Funkcje</h3>
                        <div class="features-grid">
                            <button class="feature-btn" onclick="updateDetailedProgress()">
                                <i class="fas fa-analytics"></i>
                                <span>Analiza Postępu</span>
                            </button>
                            <button class="feature-btn" onclick="exportUserData()">
                                <i class="fas fa-download"></i>
                                <span>Eksport Danych</span>
                            </button>
                            <button class="feature-btn" onclick="document.getElementById('importData').click()">
                                <i class="fas fa-upload"></i>
                                <span>Import Danych</span>
                            </button>
                            <button class="feature-btn" onclick="openVoiceModal()">
                                <i class="fas fa-microphone"></i>
                                <span>Voice Interface</span>
                            </button>
                        </div>
                        <input type="file" id="importData" accept=".json" style="display: none;">
                    </div>

                    <!-- Search Panel -->
                    <div class="dashboard-card search-panel">
                        <h3><i class="fas fa-search"></i> Wyszukiwanie</h3>
                        <div class="search-container">
                            <input type="text" id="searchInput" placeholder="Szukaj kursów, certyfikacji, wydarzeń... (Ctrl+K)" class="search-input">
                            <button class="search-btn" onclick="performSearch()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                        <div class="search-shortcuts">
                            <span class="shortcut-hint">Skróty: Ctrl+K (szukaj), Ctrl+Enter (wyślij), Esc (zamknij)</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Courses Section -->
            <section id="courses" class="section">
                <div class="section-header">
                    <h1>Kursy Coursera - Plan Certyfikacji</h1>
                    <p>5 kluczowych certyfikacji dla transformacji Manufacturing → AI</p>
                </div>

                <div class="courses-grid" id="coursesGrid">
                    <!-- Courses will be populated by JavaScript -->
                </div>
            </section>

            <!-- Certifications Section -->
            <section id="certifications" class="section">
                <div class="section-header">
                    <h1>Certyfikacje Premium</h1>
                    <p>Inwestycje w wysokiej jakości certyfikacje z najlepszym ROI</p>
                </div>

                <div class="certifications-grid" id="certificationsGrid">
                    <!-- Certifications will be populated by JavaScript -->
                </div>
            </section>

            <!-- Portfolio Section -->
            <section id="portfolio" class="section">
                <div class="section-header">
                    <h1>Portfolio Platforms</h1>
                    <p>Strategia budowy portfolio Manufacturing → AI</p>
                </div>

                <div class="portfolio-grid" id="portfolioGrid">
                    <!-- Portfolio platforms will be populated by JavaScript -->
                </div>
            </section>

            <!-- Networking Section -->
            <section id="networking" class="section">
                <div class="section-header">
                    <h1>Networking Events</h1>
                    <p>Wydarzenia AI w Polsce - budowanie sieci kontaktów</p>
                </div>

                <div class="networking-grid" id="networkingGrid">
                    <!-- Networking events will be populated by JavaScript -->
                </div>
            </section>

            <!-- Career Roadmap Section -->
            <section id="roadmap" class="section">
                <div class="section-header">
                    <h1>Mapa Kariery - 12 Miesięcy</h1>
                    <p>Szczegółowy plan transformacji z Manufacturing do AI</p>
                </div>

                <div class="roadmap-container">
                    <div class="roadmap-timeline" id="roadmapTimeline">
                        <!-- Roadmap will be populated by JavaScript -->
                    </div>
                </div>
            </section>

            <!-- Industry 4.0 Section -->
            <section id="industry40" class="section">
                <div class="section-header">
                    <h1>Industry 4.0 & Smart Manufacturing</h1>
                    <p>Specjalizacja w AI dla przemysłu automotive i electronics</p>
                </div>

                <div class="industry40-grid" id="industry40Grid">
                    <!-- Industry 4.0 content will be populated by JavaScript -->
                </div>
            </section>

            <!-- Market Intelligence Section -->
            <section id="market-intel" class="section">
                <div class="section-header">
                    <h1>Market Intelligence</h1>
                    <p>Analiza rynku AI w Polsce i automotive industry</p>
                </div>

                <div class="market-intel-grid" id="marketIntelGrid">
                    <!-- Market intelligence will be populated by JavaScript -->
                </div>
            </section>

            <!-- Manus AI Insights Section -->
            <section id="manus-insights" class="section">
                <div class="section-header">
                    <h1>🧠 Manus AI Insights</h1>
                    <p>Zaawansowana analiza AI z wykorzystaniem logiki Manus dla Andrii Zinchuk</p>
                </div>

                <div class="manus-insights-grid" id="manusInsightsGrid">
                    <!-- Manus AI insights will be populated by JavaScript -->
                </div>
            </section>
        </div>

        <!-- AI Coach ARIA Sidebar -->
        <div class="aria-sidebar">
            <div class="aria-header">
                <div class="aria-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="aria-info">
                    <h3>AI Coach ARIA</h3>
                    <p>Twój osobisty doradca kariery</p>
                    <div class="aria-status online">
                        <span class="status-dot"></span>
                        <span>Online</span>
                    </div>
                </div>
            </div>

            <div class="aria-chat" id="ariaChat">
                <div class="chat-messages" id="chatMessages">
                    <!-- Chat messages will be populated by JavaScript -->
                </div>

                <div class="quick-replies" id="quickReplies">
                    <!-- Quick reply buttons will be populated by JavaScript -->
                </div>

                <div class="chat-input-container">
                    <div class="chat-input">
                        <input type="text" id="chatInput" placeholder="Napisz wiadomość do ARIA...">
                        <button id="sendButton" class="send-btn">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                        <button id="voiceButton" class="voice-input-btn" title="Nagrywanie głosowe">
                            <i class="fas fa-microphone"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Achievement Modal -->
    <div id="achievementModal" class="modal">
        <div class="modal-content achievement-modal">
            <div class="modal-header">
                <h2><i class="fas fa-trophy"></i> Osiągnięcie Odblokowane!</h2>
                <span class="close" onclick="closeAchievementModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="achievement-details" id="achievementDetails">
                    <!-- Achievement details will be populated by JavaScript -->
                </div>
                <div class="achievement-impact" id="achievementImpact">
                    <!-- Impact analysis will be populated by JavaScript -->
                </div>
                <div class="next-steps" id="nextSteps">
                    <!-- Next action steps will be populated by JavaScript -->
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" onclick="closeAchievementModal()">Zamknij</button>
                <button class="btn-primary" onclick="continueWithARIA()">Kontynuuj z ARIA</button>
            </div>
        </div>
    </div>

    <!-- Voice Interface Modal -->
    <div id="voiceModal" class="modal">
        <div class="modal-content voice-modal">
            <div class="modal-header">
                <h2><i class="fas fa-microphone"></i> Interfejs Głosowy</h2>
                <span class="close" onclick="closeVoiceModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="voice-interface">
                    <div class="voice-animation" id="voiceAnimation">
                        <div class="voice-circle"></div>
                        <div class="voice-circle"></div>
                        <div class="voice-circle"></div>
                    </div>
                    <p id="voiceStatus">Kliknij przycisk i zacznij mówić...</p>
                    <div class="voice-controls">
                        <button id="startVoice" class="voice-control-btn">
                            <i class="fas fa-microphone"></i>
                            Rozpocznij nagrywanie
                        </button>
                        <button id="stopVoice" class="voice-control-btn" disabled>
                            <i class="fas fa-stop"></i>
                            Zatrzymaj
                        </button>
                    </div>
                    <div class="voice-transcript" id="voiceTranscript">
                        <!-- Voice transcript will appear here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="aria-coach.js"></script>
</body>
</html>
