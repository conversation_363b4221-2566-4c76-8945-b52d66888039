// Configuration file for <PERSON><PERSON><PERSON> Learning Platform
// Enhanced with Manus AI capabilities

const CONFIG = {
    // Platform Information
    platform: {
        name: "<PERSON><PERSON><PERSON> Learning Platform",
        version: "1.0.0",
        description: "Personalized learning platform enhanced with Manus AI",
        author: "Augment Agent",
        created: "2025-01-02"
    },

    // User Profile Configuration
    user: {
        name: "Dr. <PERSON><PERSON><PERSON>",
        email: "<EMAIL>",
        linkedin: "andrii-zinchuk-980121ba",
        currentRole: "Process Engineer SMT/CBA",
        company: "Aptiv",
        location: "Gdansk Metropolitan Area",
        experience: 15,
        education: "Ph.D. in Metallic Materials Engineering and Ceramics"
    },

    // API Configuration (for future integration)
    apis: {
        coursera: {
            baseUrl: "https://api.coursera.org/api/courses.v1",
            enabled: false, // Set to true when API key is available
            apiKey: "YOUR_COURSERA_API_KEY_HERE"
        },
        edx: {
            baseUrl: "https://api.edx.org/catalog/v1/courses",
            enabled: false,
            apiKey: "YOUR_EDX_API_KEY_HERE"
        },
        linkedin: {
            baseUrl: "https://api.linkedin.com/v2/learning",
            enabled: false,
            apiKey: "YOUR_LINKEDIN_API_KEY_HERE"
        },
        manusAI: {
            baseUrl: "https://api.manus.ai/v1",
            enabled: true, // Simulated for demo
            apiKey: "MANUS_AI_DEMO_KEY"
        }
    },

    // Learning Categories
    categories: {
        "ai-ml": {
            name: "AI & Machine Learning",
            color: "#4a90e2",
            icon: "fas fa-robot",
            priority: 1
        },
        "data-science": {
            name: "Data Science",
            color: "#ff6b6b",
            icon: "fas fa-chart-line",
            priority: 2
        },
        "engineering": {
            name: "Engineering",
            color: "#2ecc71",
            icon: "fas fa-cogs",
            priority: 3
        },
        "business": {
            name: "Business Intelligence",
            color: "#f39c12",
            icon: "fas fa-briefcase",
            priority: 4
        },
        "leadership": {
            name: "Leadership",
            color: "#9b59b6",
            icon: "fas fa-users",
            priority: 5
        }
    },

    // Skill Levels
    skillLevels: {
        beginner: { min: 0, max: 40, color: "#2ecc71", label: "Beginner" },
        intermediate: { min: 41, max: 70, color: "#f39c12", label: "Intermediate" },
        advanced: { min: 71, max: 90, color: "#e74c3c", label: "Advanced" },
        expert: { min: 91, max: 100, color: "#8e44ad", label: "Expert" }
    },

    // Learning Preferences
    preferences: {
        learningStyle: "visual", // visual, auditory, kinesthetic, reading
        timeCommitment: "moderate", // light, moderate, intensive
        difficulty: "intermediate", // beginner, intermediate, advanced
        focusAreas: ["ai-ml", "data-science", "engineering"],
        languages: ["English", "Polish"],
        certificationGoals: true,
        practicalProjects: true
    },

    // Manus AI Configuration
    manusAI: {
        enabled: true,
        features: {
            profileAnalysis: true,
            courseRecommendations: true,
            progressTracking: true,
            careerPredictions: true,
            contentGeneration: true,
            skillAssessment: true
        },
        analysisDepth: "comprehensive", // basic, standard, comprehensive
        updateFrequency: "weekly", // daily, weekly, monthly
        confidenceThreshold: 0.8,
        learningPathOptimization: true
    },

    // Chart Configuration
    charts: {
        defaultColors: [
            "#4a90e2", "#ff6b6b", "#2ecc71", "#f39c12", 
            "#9b59b6", "#e67e22", "#1abc9c", "#34495e"
        ],
        animations: {
            enabled: true,
            duration: 1000,
            easing: "easeInOutQuart"
        },
        responsive: true,
        maintainAspectRatio: false
    },

    // Notification Settings
    notifications: {
        enabled: true,
        types: {
            courseRecommendations: true,
            progressUpdates: true,
            achievements: true,
            deadlines: true,
            aiInsights: true
        },
        frequency: "moderate", // minimal, moderate, frequent
        sound: false,
        desktop: true
    },

    // Progress Tracking
    tracking: {
        autoSave: true,
        saveInterval: 300000, // 5 minutes in milliseconds
        syncWithCloud: false, // For future implementation
        exportFormats: ["json", "csv", "pdf"],
        retentionPeriod: 365 // days
    },

    // Course Providers
    providers: {
        coursera: {
            name: "Coursera",
            logo: "https://d3njjcbhbojbot.cloudfront.net/api/utilities/v1/imageproxy/https://coursera-university-assets.s3.amazonaws.com/fa/4c0370a2df11e698dfff9ce6e35b7b/coursera-logo-square.png",
            baseUrl: "https://www.coursera.org",
            rating: 4.5,
            specialties: ["AI/ML", "Data Science", "Business"]
        },
        edx: {
            name: "edX",
            logo: "https://www.edx.org/images/logos/edx-logo-elm.svg",
            baseUrl: "https://www.edx.org",
            rating: 4.3,
            specialties: ["Engineering", "Computer Science", "AI"]
        },
        mit: {
            name: "MIT OpenCourseWare",
            logo: "https://ocw.mit.edu/images/mit-logo.gif",
            baseUrl: "https://ocw.mit.edu",
            rating: 4.8,
            specialties: ["Advanced Engineering", "Research", "Innovation"]
        },
        datacamp: {
            name: "DataCamp",
            logo: "https://www.datacamp.com/datacamp-sq.png",
            baseUrl: "https://www.datacamp.com",
            rating: 4.4,
            specialties: ["Data Science", "Python", "Analytics"]
        }
    },

    // Career Paths
    careerPaths: {
        "data-scientist-materials": {
            title: "Senior Data Scientist (Materials)",
            description: "Combine materials expertise with advanced data science",
            timeline: "18-24 months",
            probability: 85,
            requiredSkills: ["Python", "Machine Learning", "Materials Science", "Statistics"],
            salaryRange: { min: 85000, max: 120000, currency: "EUR" }
        },
        "ai-engineering-manager": {
            title: "AI Engineering Manager",
            description: "Lead AI initiatives in manufacturing and engineering",
            timeline: "2-3 years",
            probability: 78,
            requiredSkills: ["AI/ML", "Leadership", "Project Management", "Engineering"],
            salaryRange: { min: 95000, max: 140000, currency: "EUR" }
        },
        "technical-innovation-lead": {
            title: "Technical Innovation Lead",
            description: "Drive innovation in materials and manufacturing",
            timeline: "3-4 years",
            probability: 72,
            requiredSkills: ["Innovation Management", "Technical Leadership", "AI", "Strategy"],
            salaryRange: { min: 110000, max: 160000, currency: "EUR" }
        }
    },

    // Achievement System
    achievements: {
        "python-master": {
            title: "Python Programming Master",
            description: "Achieved advanced proficiency in Python programming",
            icon: "fab fa-python",
            points: 100,
            requirements: { skill: "Python Programming", level: 80 }
        },
        "ai-pioneer": {
            title: "AI Pioneer",
            description: "Completed foundational AI and Machine Learning courses",
            icon: "fas fa-robot",
            points: 150,
            requirements: { category: "ai-ml", courses: 3 }
        },
        "data-analyst": {
            title: "Data Analysis Expert",
            description: "Mastered data analysis and visualization techniques",
            icon: "fas fa-chart-bar",
            points: 120,
            requirements: { skill: "Data Analysis", level: 75 }
        },
        "continuous-learner": {
            title: "Continuous Learner",
            description: "Maintained consistent learning for 6 months",
            icon: "fas fa-graduation-cap",
            points: 200,
            requirements: { streak: 180 } // days
        }
    },

    // UI Configuration
    ui: {
        theme: "modern", // modern, classic, minimal
        primaryColor: "#4a90e2",
        secondaryColor: "#ff6b6b",
        accentColor: "#2ecc71",
        fontFamily: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif",
        animations: {
            pageTransitions: true,
            hoverEffects: true,
            loadingAnimations: true
        },
        layout: {
            sidebar: false,
            compactMode: false,
            darkMode: false
        }
    },

    // Analytics Configuration
    analytics: {
        enabled: true,
        trackingEvents: [
            "course_view", "course_enroll", "skill_assessment", 
            "progress_update", "achievement_unlock", "recommendation_click"
        ],
        reportingFrequency: "weekly",
        dataRetention: 730, // days
        exportOptions: ["pdf", "excel", "json"]
    },

    // Security Settings
    security: {
        dataEncryption: true,
        sessionTimeout: 3600, // seconds
        maxLoginAttempts: 5,
        passwordRequirements: {
            minLength: 8,
            requireUppercase: true,
            requireNumbers: true,
            requireSpecialChars: true
        }
    },

    // Development Settings
    development: {
        debug: true,
        mockData: true,
        apiSimulation: true,
        performanceLogging: true,
        errorReporting: true
    }
};

// Export configuration for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CONFIG;
} else if (typeof window !== 'undefined') {
    window.CONFIG = CONFIG;
}

// Utility functions for configuration
const ConfigUtils = {
    // Get skill level based on score
    getSkillLevel: (score) => {
        for (const [level, range] of Object.entries(CONFIG.skillLevels)) {
            if (score >= range.min && score <= range.max) {
                return { level, ...range };
            }
        }
        return CONFIG.skillLevels.beginner;
    },

    // Get category configuration
    getCategory: (categoryId) => {
        return CONFIG.categories[categoryId] || null;
    },

    // Get career path information
    getCareerPath: (pathId) => {
        return CONFIG.careerPaths[pathId] || null;
    },

    // Check if feature is enabled
    isFeatureEnabled: (feature) => {
        return CONFIG.manusAI.features[feature] || false;
    },

    // Get provider information
    getProvider: (providerId) => {
        return CONFIG.providers[providerId] || null;
    },

    // Validate API configuration
    validateApiConfig: () => {
        const issues = [];
        for (const [api, config] of Object.entries(CONFIG.apis)) {
            if (config.enabled && !config.apiKey) {
                issues.push(`Missing API key for ${api}`);
            }
        }
        return issues;
    }
};

// Make utility functions available globally
if (typeof window !== 'undefined') {
    window.ConfigUtils = ConfigUtils;
}
