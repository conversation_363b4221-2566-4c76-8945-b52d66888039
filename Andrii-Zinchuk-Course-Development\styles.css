/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Particles Background */
#particles-js {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: -1;
}

/* Navigation */
.navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: bold;
    color: #4a90e2;
}

.nav-logo i {
    margin-right: 10px;
    font-size: 1.8rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 30px;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: #4a90e2;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: #4a90e2;
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

/* Hero Section */
.hero {
    padding: 120px 0 80px;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
    font-size: 1.3rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 40px;
    line-height: 1.6;
}

.hero-stats {
    display: flex;
    gap: 40px;
    margin-bottom: 40px;
}

.stat {
    text-align: center;
    color: white;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.cta-button {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
    border: none;
    padding: 15px 30px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
}

.cta-button i {
    margin-right: 10px;
}

/* Profile Card */
.profile-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    transform: perspective(1000px) rotateY(-5deg);
    transition: transform 0.3s ease;
}

.profile-card:hover {
    transform: perspective(1000px) rotateY(0deg);
}

.profile-img {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    margin-bottom: 20px;
    border: 5px solid #4a90e2;
}

.profile-card h3 {
    font-size: 1.5rem;
    color: #333;
    margin-bottom: 10px;
}

.profile-card p {
    color: #666;
    margin-bottom: 20px;
}

.skills-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
}

.skill-tag {
    background: linear-gradient(45deg, #4a90e2, #357abd);
    color: white;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* Dashboard Section */
.dashboard {
    padding: 80px 0;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    color: white;
    margin-bottom: 60px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.dashboard-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-5px);
}

.card-header {
    margin-bottom: 20px;
}

.card-header h3 {
    color: #333;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.card-header i {
    color: #4a90e2;
}

/* Profile Section */
.profile-section {
    padding: 80px 0;
    background: rgba(255, 255, 255, 0.05);
}

.profile-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
}

.info-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 30px;
    border-radius: 15px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.info-card h3 {
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.info-card i {
    color: #4a90e2;
}

.info-content p {
    margin-bottom: 10px;
    line-height: 1.6;
}

.certification-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.cert-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    background: rgba(74, 144, 226, 0.1);
    border-radius: 8px;
}

.cert-item i {
    color: #4a90e2;
}

/* Skills Analysis */
.skills-grid {
    display: grid;
    gap: 30px;
}

.skill-category h4 {
    color: #333;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.skill-bar {
    margin-bottom: 15px;
}

.skill-bar span {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #555;
}

.bar {
    background: #e0e0e0;
    height: 8px;
    border-radius: 4px;
    overflow: hidden;
}

.fill {
    height: 100%;
    background: linear-gradient(45deg, #4a90e2, #357abd);
    border-radius: 4px;
    transition: width 1s ease;
}

/* Courses Section */
.courses-section {
    padding: 80px 0;
    background: rgba(255, 255, 255, 0.1);
}

.course-filters {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.filter-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.filter-btn:hover,
.filter-btn.active {
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    border-color: rgba(255, 255, 255, 0.9);
}

.courses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

/* Progress Section */
.progress-section {
    padding: 80px 0;
    background: rgba(255, 255, 255, 0.05);
}

.progress-grid {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 40px;
}

.progress-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.progress-card h3 {
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.achievements-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.achievement {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px;
    background: linear-gradient(45deg, #4a90e2, #357abd);
    color: white;
    border-radius: 10px;
    font-weight: 500;
}

/* Roadmap Section */
.roadmap-section {
    padding: 80px 0;
    background: rgba(255, 255, 255, 0.1);
}

.roadmap-container {
    max-width: 800px;
    margin: 0 auto;
}

/* Footer */
.footer {
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 40px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-bottom: 30px;
}

.footer-section h3 {
    margin-bottom: 15px;
    color: #4a90e2;
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    opacity: 0.7;
}

/* Loading Animation */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100px;
    font-style: italic;
    color: #666;
}

/* Course Management Styles */
.course-management {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 25px;
    border-radius: 15px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.management-controls {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.add-course-btn,
.import-coursera-btn,
.refresh-recommendations-btn {
    background: linear-gradient(45deg, #4a90e2, #357abd);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.add-course-btn:hover,
.import-coursera-btn:hover,
.refresh-recommendations-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(74, 144, 226, 0.4);
}

.import-coursera-btn {
    background: linear-gradient(45deg, #0056d3, #004bb5);
}

.refresh-recommendations-btn {
    background: linear-gradient(45deg, #2ecc71, #27ae60);
}

.course-stats {
    display: flex;
    gap: 30px;
    flex-wrap: wrap;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px;
    background: rgba(74, 144, 226, 0.1);
    border-radius: 10px;
    min-width: 120px;
}

.stat-label {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 5px;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #4a90e2;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: white;
    margin: 2% auto;
    padding: 0;
    border-radius: 15px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    background: linear-gradient(45deg, #4a90e2, #357abd);
    color: white;
    padding: 20px;
    border-radius: 15px 15px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
}

.close {
    color: white;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover {
    color: #ff6b6b;
}

.modal-body {
    padding: 30px;
}

.modal-footer {
    padding: 20px 30px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #4a90e2;
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

/* Button Styles */
.btn-primary,
.btn-secondary {
    padding: 12px 25px;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.btn-primary {
    background: linear-gradient(45deg, #4a90e2, #357abd);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(74, 144, 226, 0.4);
}

.btn-secondary {
    background: #f8f9fa;
    color: #333;
    border: 2px solid #e0e0e0;
}

.btn-secondary:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

/* Coursera Import Styles */
.coursera-categories h3 {
    color: #333;
    margin-bottom: 15px;
}

.category-checkboxes {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    background: rgba(74, 144, 226, 0.05);
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.checkbox-label:hover {
    background: rgba(74, 144, 226, 0.1);
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.import-progress {
    margin-top: 20px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(45deg, #4a90e2, #357abd);
    width: 0%;
    transition: width 0.3s ease;
}

#progressText {
    text-align: center;
    color: #666;
    font-style: italic;
}

/* Course Card Enhancements */
.course-card {
    position: relative;
}

.course-card .course-actions {
    position: absolute;
    top: 15px;
    right: 15px;
    display: flex;
    gap: 8px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.course-card:hover .course-actions {
    opacity: 1;
}

.action-btn {
    width: 35px;
    height: 35px;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.edit-btn {
    background: #f39c12;
    color: white;
}

.delete-btn {
    background: #e74c3c;
    color: white;
}

.action-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.course-status {
    position: absolute;
    top: 15px;
    left: 15px;
    padding: 4px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-available {
    background: #e8f5e8;
    color: #2e7d32;
}

.status-enrolled {
    background: #e3f2fd;
    color: #1976d2;
}

.status-in-progress {
    background: #fff3e0;
    color: #f57c00;
}

.status-completed {
    background: #e8f5e8;
    color: #2e7d32;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .profile-grid {
        grid-template-columns: 1fr;
    }

    .progress-grid {
        grid-template-columns: 1fr;
    }

    .nav-menu {
        display: none;
    }

    .management-controls {
        flex-direction: column;
    }

    .course-stats {
        justify-content: center;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 95%;
        margin: 5% auto;
    }

    .modal-footer {
        flex-direction: column;
    }
}

/* No courses state */
.no-courses {
    text-align: center;
    padding: 60px 20px;
    color: #666;
    grid-column: 1 / -1;
}

.no-courses i {
    color: #4a90e2;
    margin-bottom: 20px;
}

.no-courses h3 {
    margin-bottom: 15px;
    color: #333;
}

/* Enhanced course card animations */
.course-card {
    animation: courseCardSlideIn 0.6s ease forwards;
    opacity: 0;
    transform: translateY(20px);
}

.course-card:nth-child(1) { animation-delay: 0.1s; }
.course-card:nth-child(2) { animation-delay: 0.2s; }
.course-card:nth-child(3) { animation-delay: 0.3s; }
.course-card:nth-child(4) { animation-delay: 0.4s; }
.course-card:nth-child(5) { animation-delay: 0.5s; }
.course-card:nth-child(6) { animation-delay: 0.6s; }

@keyframes courseCardSlideIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced notification styles */
.notification {
    animation: notificationSlideIn 0.3s ease;
}

.notification.error {
    border-left: 4px solid #e74c3c;
    color: #e74c3c;
}

@keyframes notificationSlideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Course provider specific styling */
.course-provider {
    position: relative;
}

.course-provider::before {
    content: '';
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 8px;
    background-size: contain;
    background-repeat: no-repeat;
    vertical-align: middle;
}

.course-card[data-provider="Coursera"] .course-provider::before {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%230056d3"><path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm0 22C6.486 22 2 17.514 2 12S6.486 2 12 2s10 4.486 10 10-4.486 10-10 10z"/></svg>');
}

/* Loading states */
.loading-state {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #666;
}

.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #e0e0e0;
    border-top: 2px solid #4a90e2;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Course status indicators */
.course-status {
    font-size: 0.7rem;
    letter-spacing: 0.5px;
}

.status-available::before {
    content: '●';
    margin-right: 5px;
    color: #2e7d32;
}

.status-enrolled::before {
    content: '●';
    margin-right: 5px;
    color: #1976d2;
}

.status-in-progress::before {
    content: '●';
    margin-right: 5px;
    color: #f57c00;
}

.status-completed::before {
    content: '✓';
    margin-right: 5px;
    color: #2e7d32;
    font-weight: bold;
}

/* Enhanced hover effects */
.course-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.course-card:hover .course-actions {
    opacity: 1;
}

/* Skill tags enhancement */
.skill-tag {
    transition: all 0.3s ease;
}

.skill-tag:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(74, 144, 226, 0.3);
}

/* Form validation styles */
.form-group input:invalid,
.form-group select:invalid,
.form-group textarea:invalid {
    border-color: #e74c3c;
}

.form-group input:valid,
.form-group select:valid,
.form-group textarea:valid {
    border-color: #2ecc71;
}

/* Success states */
.success-message {
    background: linear-gradient(45deg, #2ecc71, #27ae60);
    color: white;
    padding: 15px 20px;
    border-radius: 10px;
    margin: 20px 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.success-message i {
    font-size: 1.2rem;
}

/* Course grid responsive enhancements */
@media (max-width: 1200px) {
    .courses-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
}

@media (max-width: 768px) {
    .course-card .course-actions {
        opacity: 1;
        position: static;
        justify-content: center;
        margin-top: 15px;
    }

    .course-status {
        position: static;
        display: inline-block;
        margin-bottom: 10px;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dashboard-card,
.info-card,
.progress-card {
    animation: fadeInUp 0.6s ease forwards;
}

/* Hover Effects */
.dashboard-card:hover,
.info-card:hover,
.progress-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}
