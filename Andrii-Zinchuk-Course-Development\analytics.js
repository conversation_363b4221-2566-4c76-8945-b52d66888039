// Analytics Dashboard for <PERSON><PERSON><PERSON> Learning Platform
// Enhanced with Manus AI predictive capabilities

// Learning data based on <PERSON><PERSON><PERSON>'s profile and projected growth
const learningData = {
    skillProgression: {
        labels: ['Jan 2024', 'Feb 2024', 'Mar 2024', 'Apr 2024', 'May 2024', 'Jun 2024'],
        datasets: [
            {
                label: 'Python Programming',
                data: [45, 52, 58, 65, 68, 70],
                borderColor: '#4a90e2',
                backgroundColor: 'rgba(74, 144, 226, 0.1)',
                tension: 0.4
            },
            {
                label: 'Data Analysis',
                data: [40, 45, 50, 58, 62, 65],
                borderColor: '#ff6b6b',
                backgroundColor: 'rgba(255, 107, 107, 0.1)',
                tension: 0.4
            },
            {
                label: 'Machine Learning',
                data: [30, 35, 42, 48, 55, 60],
                borderColor: '#f39c12',
                backgroundColor: 'rgba(243, 156, 18, 0.1)',
                tension: 0.4
            },
            {
                label: 'Materials Science',
                data: [90, 91, 92, 93, 94, 95],
                borderColor: '#2ecc71',
                backgroundColor: 'rgba(46, 204, 113, 0.1)',
                tension: 0.4
            }
        ]
    },
    
    focusAreas: {
        labels: ['AI/ML', 'Data Science', 'Materials Eng.', 'Quality Mgmt', 'Business Analysis', 'Leadership'],
        datasets: [{
            data: [25, 20, 30, 10, 10, 5],
            backgroundColor: [
                '#4a90e2',
                '#ff6b6b',
                '#2ecc71',
                '#f39c12',
                '#9b59b6',
                '#e67e22'
            ],
            borderWidth: 0
        }]
    },
    
    monthlyActivity: {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        datasets: [{
            label: 'Learning Hours',
            data: [32, 45, 38, 52, 48, 55],
            backgroundColor: 'rgba(74, 144, 226, 0.8)',
            borderColor: '#4a90e2',
            borderWidth: 2
        }]
    },
    
    completionRate: {
        labels: ['Q1 2024', 'Q2 2024'],
        datasets: [{
            label: 'Completion Rate %',
            data: [78, 85],
            backgroundColor: ['#ff6b6b', '#2ecc71'],
            borderWidth: 0
        }]
    }
};

// Timeline data for learning milestones
const learningTimeline = [
    {
        date: '2024-01-15',
        title: 'Started Python Programming Course',
        description: 'Began foundational Python programming with focus on data analysis',
        type: 'course-start',
        icon: 'fas fa-play'
    },
    {
        date: '2024-02-28',
        title: 'Completed Python Fundamentals',
        description: 'Successfully completed Python basics with 92% score',
        type: 'completion',
        icon: 'fas fa-check-circle'
    },
    {
        date: '2024-03-15',
        title: 'RPA Professional Certification',
        description: 'Earned SS&C Blue Prism RPA Professional Certificate',
        type: 'certification',
        icon: 'fas fa-certificate'
    },
    {
        date: '2024-04-10',
        title: 'Started AI for Manufacturing',
        description: 'Enrolled in specialized AI course for manufacturing applications',
        type: 'course-start',
        icon: 'fas fa-robot'
    },
    {
        date: '2024-05-20',
        title: 'Data Analysis Milestone',
        description: 'Reached intermediate level in data analysis and visualization',
        type: 'milestone',
        icon: 'fas fa-chart-line'
    },
    {
        date: '2024-06-01',
        title: 'Machine Learning Fundamentals',
        description: 'Started comprehensive ML course with engineering focus',
        type: 'course-start',
        icon: 'fas fa-brain'
    }
];

// Career prediction data
const careerPredictions = {
    skillForecast: {
        labels: ['Current', '6 Months', '1 Year', '2 Years'],
        datasets: [
            {
                label: 'AI/ML Skills',
                data: [60, 75, 85, 92],
                borderColor: '#4a90e2',
                backgroundColor: 'rgba(74, 144, 226, 0.2)',
                tension: 0.4,
                fill: true
            },
            {
                label: 'Data Science',
                data: [65, 78, 88, 94],
                borderColor: '#ff6b6b',
                backgroundColor: 'rgba(255, 107, 107, 0.2)',
                tension: 0.4,
                fill: true
            },
            {
                label: 'Leadership',
                data: [70, 75, 82, 90],
                borderColor: '#2ecc71',
                backgroundColor: 'rgba(46, 204, 113, 0.2)',
                tension: 0.4,
                fill: true
            }
        ]
    }
};

// Initialize analytics dashboard
document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
    populateTimeline();
    animateMetrics();
    setupInteractivity();
});

// Initialize all charts
function initializeCharts() {
    // Skill Progression Chart
    const skillProgressCtx = document.getElementById('skillProgressChart').getContext('2d');
    new Chart(skillProgressCtx, {
        type: 'line',
        data: learningData.skillProgression,
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'Skill Development Over Time'
                },
                legend: {
                    position: 'bottom'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    title: {
                        display: true,
                        text: 'Skill Level (%)'
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    });

    // Focus Areas Chart
    const focusAreasCtx = document.getElementById('focusAreasChart').getContext('2d');
    new Chart(focusAreasCtx, {
        type: 'doughnut',
        data: learningData.focusAreas,
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'Learning Time Distribution'
                },
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Competency Matrix Chart
    const competencyCtx = document.getElementById('competencyChart').getContext('2d');
    new Chart(competencyCtx, {
        type: 'radar',
        data: {
            labels: ['Technical Skills', 'Problem Solving', 'Communication', 'Leadership', 'Innovation', 'Adaptability'],
            datasets: [{
                label: 'Current Level',
                data: [88, 85, 75, 70, 80, 90],
                backgroundColor: 'rgba(74, 144, 226, 0.2)',
                borderColor: '#4a90e2',
                borderWidth: 2
            }, {
                label: 'Target Level',
                data: [95, 90, 85, 88, 92, 95],
                backgroundColor: 'rgba(255, 107, 107, 0.2)',
                borderColor: '#ff6b6b',
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'Competency Assessment'
                }
            },
            scales: {
                r: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });

    // Monthly Activity Chart
    const monthlyActivityCtx = document.getElementById('monthlyActivityChart').getContext('2d');
    new Chart(monthlyActivityCtx, {
        type: 'bar',
        data: learningData.monthlyActivity,
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'Monthly Learning Hours'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Hours'
                    }
                }
            }
        }
    });

    // Completion Rate Chart
    const completionRateCtx = document.getElementById('completionRateChart').getContext('2d');
    new Chart(completionRateCtx, {
        type: 'doughnut',
        data: learningData.completionRate,
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'Course Completion Rates'
                }
            }
        }
    });

    // Skill Forecast Chart
    const skillForecastCtx = document.getElementById('skillForecastChart').getContext('2d');
    new Chart(skillForecastCtx, {
        type: 'line',
        data: careerPredictions.skillForecast,
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'Predicted Skill Development'
                },
                legend: {
                    position: 'bottom'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    title: {
                        display: true,
                        text: 'Skill Level (%)'
                    }
                }
            }
        }
    });
}

// Populate learning timeline
function populateTimeline() {
    const timelineContainer = document.getElementById('learningTimeline');
    
    timelineContainer.innerHTML = learningTimeline.map((item, index) => `
        <div class="timeline-item ${item.type}" style="animation-delay: ${index * 0.1}s">
            <div class="timeline-marker">
                <i class="${item.icon}"></i>
            </div>
            <div class="timeline-content">
                <div class="timeline-date">${formatDate(item.date)}</div>
                <h4 class="timeline-title">${item.title}</h4>
                <p class="timeline-description">${item.description}</p>
            </div>
        </div>
    `).join('');
}

// Animate metrics on page load
function animateMetrics() {
    const metricValues = document.querySelectorAll('.metric-value');
    
    metricValues.forEach(metric => {
        const finalValue = parseInt(metric.textContent);
        let currentValue = 0;
        const increment = finalValue / 50;
        
        const animation = setInterval(() => {
            currentValue += increment;
            if (currentValue >= finalValue) {
                currentValue = finalValue;
                clearInterval(animation);
            }
            
            if (metric.textContent.includes('%')) {
                metric.textContent = Math.round(currentValue) + '%';
            } else {
                metric.textContent = Math.round(currentValue);
            }
        }, 30);
    });
}

// Setup interactive features
function setupInteractivity() {
    // Add hover effects to metric cards
    const metricCards = document.querySelectorAll('.metric-card');
    metricCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // Add click handlers for recommendation action buttons
    const actionButtons = document.querySelectorAll('.rec-action-btn');
    actionButtons.forEach(button => {
        button.addEventListener('click', function() {
            const cardTitle = this.closest('.recommendation-card').querySelector('h4').textContent;
            handleRecommendationAction(cardTitle);
        });
    });

    // Add smooth scrolling for navigation
    document.querySelectorAll('.nav-link').forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const targetId = link.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            if (targetSection) {
                targetSection.scrollIntoView({ behavior: 'smooth' });
            }
        });
    });
}

// Handle recommendation actions
function handleRecommendationAction(title) {
    // Simulate Manus AI integration
    const actions = {
        'Advanced Machine Learning for Materials': () => {
            showNotification('Generating personalized ML learning path...', 'info');
            setTimeout(() => {
                showNotification('Learning path created! Check your dashboard for new courses.', 'success');
            }, 2000);
        },
        'Leadership and Management Skills': () => {
            showNotification('Exploring leadership courses...', 'info');
            setTimeout(() => {
                showNotification('Found 5 relevant leadership courses. Added to your recommendations.', 'success');
            }, 1500);
        },
        'Business Strategy and Innovation': () => {
            showNotification('Planning future learning path...', 'info');
            setTimeout(() => {
                showNotification('Business strategy courses scheduled for Q3 2024.', 'success');
            }, 1000);
        }
    };

    if (actions[title]) {
        actions[title]();
    }
}

// Show notification
function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-info-circle'}"></i>
        <span>${message}</span>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
    
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Format date for timeline
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'short', 
        day: 'numeric' 
    });
}

// Add CSS for analytics-specific styles
const analyticsStyles = `
    .hero-small {
        padding: 120px 0 60px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        text-align: center;
        color: white;
    }
    
    .hero-small h1 {
        font-size: 2.5rem;
        margin-bottom: 15px;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    }
    
    .analytics-section {
        padding: 60px 0;
        background: rgba(255, 255, 255, 0.05);
    }
    
    .analytics-section:nth-child(even) {
        background: rgba(255, 255, 255, 0.1);
    }
    
    .metrics-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 30px;
        margin-bottom: 40px;
    }
    
    .metric-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        padding: 30px;
        border-radius: 15px;
        display: flex;
        align-items: center;
        gap: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }
    
    .metric-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(45deg, #4a90e2, #357abd);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
    }
    
    .metric-content h3 {
        color: #333;
        font-size: 0.9rem;
        margin-bottom: 10px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .metric-value {
        font-size: 2.5rem;
        font-weight: bold;
        color: #4a90e2;
        display: block;
        margin-bottom: 5px;
    }
    
    .metric-change {
        font-size: 0.8rem;
        font-weight: 600;
    }
    
    .metric-change.positive {
        color: #2ecc71;
    }
    
    .metric-change.negative {
        color: #e74c3c;
    }
    
    .analysis-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 30px;
    }
    
    .chart-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        padding: 25px;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }
    
    .chart-container h3 {
        color: #333;
        margin-bottom: 20px;
        text-align: center;
    }
    
    .insights-panel {
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
        padding: 25px;
        border-radius: 15px;
    }
    
    .insights-panel h3 {
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .insight-list {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }
    
    .insight-item {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 10px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
    }
    
    .progress-dashboard {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 40px;
    }
    
    .progress-timeline {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        padding: 30px;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }
    
    .timeline-container {
        position: relative;
        padding-left: 30px;
    }
    
    .timeline-container::before {
        content: '';
        position: absolute;
        left: 15px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: linear-gradient(to bottom, #4a90e2, #357abd);
    }
    
    .timeline-item {
        position: relative;
        margin-bottom: 30px;
        opacity: 0;
        animation: fadeInUp 0.6s ease forwards;
    }
    
    .timeline-marker {
        position: absolute;
        left: -22px;
        top: 0;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background: #4a90e2;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.8rem;
    }
    
    .timeline-content {
        background: rgba(74, 144, 226, 0.05);
        padding: 15px;
        border-radius: 10px;
        border-left: 3px solid #4a90e2;
    }
    
    .timeline-date {
        color: #4a90e2;
        font-size: 0.8rem;
        font-weight: 600;
        margin-bottom: 5px;
    }
    
    .timeline-title {
        color: #333;
        margin-bottom: 8px;
        font-size: 1.1rem;
    }
    
    .timeline-description {
        color: #666;
        font-size: 0.9rem;
        line-height: 1.5;
    }
    
    .progress-charts {
        display: flex;
        flex-direction: column;
        gap: 30px;
    }
    
    .chart-wrapper {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        padding: 25px;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }
    
    .chart-wrapper h3 {
        color: #333;
        margin-bottom: 20px;
        text-align: center;
    }
    
    .predictions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 30px;
    }
    
    .prediction-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }
    
    .prediction-header {
        background: linear-gradient(45deg, #4a90e2, #357abd);
        color: white;
        padding: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .prediction-header h3 {
        margin: 0;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .confidence-badge {
        background: rgba(255, 255, 255, 0.2);
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
    }
    
    .prediction-content {
        padding: 25px;
    }
    
    .career-paths {
        margin-top: 20px;
    }
    
    .path-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px;
        background: rgba(74, 144, 226, 0.05);
        border-radius: 8px;
        margin-bottom: 10px;
    }
    
    .path-title {
        font-weight: 600;
        color: #333;
    }
    
    .path-probability {
        background: #4a90e2;
        color: white;
        padding: 4px 10px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
    }
    
    .path-timeline {
        font-size: 0.8rem;
        color: #666;
        margin-top: 5px;
    }
    
    .salary-projection {
        display: flex;
        flex-direction: column;
        gap: 15px;
        margin-bottom: 20px;
    }
    
    .salary-projection > div {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px;
        background: rgba(74, 144, 226, 0.05);
        border-radius: 8px;
    }
    
    .salary-projection .label {
        font-weight: 600;
        color: #333;
    }
    
    .salary-projection .value {
        font-weight: bold;
        color: #4a90e2;
    }
    
    .salary-projection .value.positive {
        color: #2ecc71;
    }
    
    .salary-factors h4 {
        color: #333;
        margin-bottom: 10px;
    }
    
    .salary-factors ul {
        list-style: none;
        padding: 0;
    }
    
    .salary-factors li {
        padding: 5px 0;
        color: #666;
        position: relative;
        padding-left: 20px;
    }
    
    .salary-factors li::before {
        content: '✓';
        position: absolute;
        left: 0;
        color: #2ecc71;
        font-weight: bold;
    }
    
    .recommendations-section {
        padding: 60px 0;
        background: rgba(255, 255, 255, 0.1);
    }
    
    .recommendations-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 30px;
    }
    
    .recommendation-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
    }
    
    .recommendation-card:hover {
        transform: translateY(-5px);
    }
    
    .rec-header {
        padding: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .priority-high .rec-header {
        background: linear-gradient(45deg, #e74c3c, #c0392b);
        color: white;
    }
    
    .priority-medium .rec-header {
        background: linear-gradient(45deg, #f39c12, #e67e22);
        color: white;
    }
    
    .priority-low .rec-header {
        background: linear-gradient(45deg, #3498db, #2980b9);
        color: white;
    }
    
    .rec-header h3 {
        margin: 0;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .priority-badge {
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        background: rgba(255, 255, 255, 0.2);
    }
    
    .rec-content {
        padding: 25px;
    }
    
    .rec-content h4 {
        color: #333;
        margin-bottom: 15px;
        font-size: 1.2rem;
    }
    
    .rec-content p {
        color: #666;
        line-height: 1.6;
        margin-bottom: 15px;
    }
    
    .rec-impact {
        background: rgba(74, 144, 226, 0.1);
        padding: 10px;
        border-radius: 8px;
        margin-bottom: 20px;
        color: #4a90e2;
        font-weight: 600;
        font-size: 0.9rem;
    }
    
    .rec-action-btn {
        background: linear-gradient(45deg, #4a90e2, #357abd);
        color: white;
        border: none;
        padding: 12px 25px;
        border-radius: 25px;
        cursor: pointer;
        font-weight: 600;
        transition: all 0.3s ease;
        width: 100%;
    }
    
    .rec-action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(74, 144, 226, 0.4);
    }
    
    .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        background: white;
        padding: 15px 20px;
        border-radius: 10px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        display: flex;
        align-items: center;
        gap: 10px;
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    }
    
    .notification.show {
        transform: translateX(0);
    }
    
    .notification.success {
        border-left: 4px solid #2ecc71;
        color: #2ecc71;
    }
    
    .notification.info {
        border-left: 4px solid #4a90e2;
        color: #4a90e2;
    }
    
    @media (max-width: 768px) {
        .progress-dashboard {
            grid-template-columns: 1fr;
        }
        
        .analysis-grid {
            grid-template-columns: 1fr;
        }
        
        .predictions-grid {
            grid-template-columns: 1fr;
        }
        
        .recommendations-grid {
            grid-template-columns: 1fr;
        }
    }
`;

// Add the analytics styles to the page
const styleSheet = document.createElement('style');
styleSheet.textContent = analyticsStyles;
document.head.appendChild(styleSheet);
